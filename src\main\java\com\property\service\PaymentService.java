package com.property.service;

import com.property.dao.FeeTypeDAO;
import com.property.dao.PaymentBillDAO;
import com.property.dao.PaymentRecordDAO;
import com.property.model.FeeType;
import com.property.model.PaymentBill;
import com.property.model.PaymentRecord;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 缴费管理服务类
 * 提供缴费管理相关的业务逻辑
 */
public class PaymentService {
    private FeeTypeDAO feeTypeDAO;
    private PaymentBillDAO paymentBillDAO;
    private PaymentRecordDAO paymentRecordDAO;

    public PaymentService() {
        feeTypeDAO = new FeeTypeDAO();
        paymentBillDAO = new PaymentBillDAO();
        paymentRecordDAO = new PaymentRecordDAO();
    }

    // ==================== 费用类型管理 ====================

    /**
     * 获取所有费用类型
     * @return 费用类型列表
     */
    public List<FeeType> getAllFeeTypes() {
        return feeTypeDAO.getAllFeeTypes();
    }

    /**
     * 获取所有启用的费用类型
     * @return 启用的费用类型列表
     */
    public List<FeeType> getActiveFeeTypes() {
        return feeTypeDAO.getActiveFeeTypes();
    }

    /**
     * 根据ID获取费用类型
     * @param id 费用类型ID
     * @return 费用类型对象
     */
    public FeeType getFeeTypeById(int id) {
        return feeTypeDAO.getFeeTypeById(id);
    }

    /**
     * 添加费用类型
     * @param feeType 费用类型对象
     * @return 添加成功返回true，否则返回false
     */
    public boolean addFeeType(FeeType feeType) {
        // 检查费用类型名称是否已存在
        if (feeTypeDAO.isFeeTypeNameExists(feeType.getName(), 0)) {
            return false;
        }
        return feeTypeDAO.addFeeType(feeType);
    }

    /**
     * 更新费用类型
     * @param feeType 费用类型对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateFeeType(FeeType feeType) {
        // 检查费用类型名称是否已存在
        if (feeTypeDAO.isFeeTypeNameExists(feeType.getName(), feeType.getId())) {
            return false;
        }
        return feeTypeDAO.updateFeeType(feeType);
    }

    /**
     * 删除费用类型
     * @param id 费用类型ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteFeeType(int id) {
        return feeTypeDAO.deleteFeeType(id);
    }

    // ==================== 账单管理 ====================

    /**
     * 获取所有账单
     * @return 账单列表
     */
    public List<PaymentBill> getAllBills() {
        return paymentBillDAO.getAllBills();
    }

    /**
     * 根据业主ID获取账单
     * @param ownerId 业主ID
     * @return 账单列表
     */
    public List<PaymentBill> getBillsByOwnerId(int ownerId) {
        return paymentBillDAO.getBillsByOwnerId(ownerId);
    }

    /**
     * 根据状态获取账单
     * @param status 账单状态
     * @return 账单列表
     */
    public List<PaymentBill> getBillsByStatus(String status) {
        return paymentBillDAO.getBillsByStatus(status);
    }

    // 已移除获取欠费账单方法

    /**
     * 根据ID获取账单
     * @param id 账单ID
     * @return 账单对象
     */
    public PaymentBill getBillById(int id) {
        return paymentBillDAO.getBillById(id);
    }

    /**
     * 添加账单
     * @param bill 账单对象
     * @return 添加成功返回生成的ID，失败返回-1
     */
    public int addBill(PaymentBill bill) {
        // 生成账单编号
        bill.setBillNo(paymentBillDAO.generateBillNo());
        // 设置初始状态
        bill.setStatus("未缴费");
        // 设置已缴金额为0
        bill.setPaidAmount(BigDecimal.ZERO);

        return paymentBillDAO.addBill(bill);
    }

    /**
     * 更新账单
     * @param bill 账单对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateBill(PaymentBill bill) {
        return paymentBillDAO.updateBill(bill);
    }

    /**
     * 删除账单
     * @param id 账单ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteBill(int id) {
        return paymentBillDAO.deleteBill(id);
    }

    /**
     * 批量生成账单
     * @param feeTypeId 费用类型ID
     * @param ownerIds 业主ID列表
     * @param billingPeriod 账单周期
     * @param dueDate 缴费截止日期
     * @param remark 备注
     * @return 成功生成的账单数量
     */
    public int generateBills(int feeTypeId, List<Integer> ownerIds, String billingPeriod,
                            Date dueDate, String remark) {
        int successCount = 0;
        FeeType feeType = feeTypeDAO.getFeeTypeById(feeTypeId);

        if (feeType == null) {
            return 0;
        }

        for (int ownerId : ownerIds) {
            PaymentBill bill = new PaymentBill();
            bill.setBillNo(paymentBillDAO.generateBillNo());
            bill.setOwnerId(ownerId);
            bill.setFeeTypeId(feeTypeId);
            bill.setBillingPeriod(billingPeriod);
            bill.setAmount(feeType.getUnitPrice()); // 这里简化处理，实际应根据计费方式计算
            bill.setPaidAmount(BigDecimal.ZERO);
            bill.setStatus("未缴费");
            bill.setDueDate(dueDate);
            bill.setRemark(remark);

            int id = paymentBillDAO.addBill(bill);
            if (id > 0) {
                successCount++;
            }
        }

        return successCount;
    }

    /**
     * 处理缴费
     * @param billId 账单ID
     * @param paymentAmount 缴费金额
     * @param paymentMethod 缴费方式
     * @param operator 操作员
     * @param remark 备注
     * @return 处理成功返回true，否则返回false
     */
    public boolean processPayment(int billId, BigDecimal paymentAmount, String paymentMethod,
                                 String operator, String remark) {
        // 获取账单
        PaymentBill bill = paymentBillDAO.getBillById(billId);
        if (bill == null) {
            return false;
        }

        // 创建缴费记录
        PaymentRecord record = new PaymentRecord();
        record.setRecordNo(paymentRecordDAO.generateRecordNo());
        record.setBillId(billId);
        record.setOwnerId(bill.getOwnerId());
        record.setPaymentAmount(paymentAmount);
        record.setPaymentMethod(paymentMethod);
        record.setPaymentTime(Timestamp.valueOf(LocalDateTime.now()));
        record.setOperator(operator);
        record.setReceiptNo(paymentRecordDAO.generateReceiptNo());
        record.setRemark(remark);

        // 添加缴费记录
        int recordId = paymentRecordDAO.addRecord(record);
        if (recordId <= 0) {
            return false;
        }

        // 更新账单状态和已缴金额
        BigDecimal newPaidAmount = bill.getPaidAmount().add(paymentAmount);
        String newStatus;

        if (newPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
            newStatus = "未缴费";
        } else if (newPaidAmount.compareTo(bill.getAmount()) >= 0) {
            newStatus = "已缴费";
        } else {
            newStatus = "部分缴费";
        }

        // 逾期检查功能已移除

        return paymentBillDAO.updateBillPayment(billId, newPaidAmount, newStatus);
    }

    // 逾期检查功能已移除

    // ==================== 缴费记录管理 ====================

    /**
     * 获取所有缴费记录
     * @return 缴费记录列表
     */
    public List<PaymentRecord> getAllRecords() {
        return paymentRecordDAO.getAllRecords();
    }

    /**
     * 根据业主ID获取缴费记录
     * @param ownerId 业主ID
     * @return 缴费记录列表
     */
    public List<PaymentRecord> getRecordsByOwnerId(int ownerId) {
        return paymentRecordDAO.getRecordsByOwnerId(ownerId);
    }

    /**
     * 根据账单ID获取缴费记录
     * @param billId 账单ID
     * @return 缴费记录列表
     */
    public List<PaymentRecord> getRecordsByBillId(int billId) {
        return paymentRecordDAO.getRecordsByBillId(billId);
    }

    /**
     * 根据ID获取缴费记录
     * @param id 缴费记录ID
     * @return 缴费记录对象
     */
    public PaymentRecord getRecordById(int id) {
        return paymentRecordDAO.getRecordById(id);
    }

    /**
     * 删除缴费记录
     * @param id 缴费记录ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteRecord(int id) {
        // 获取缴费记录
        PaymentRecord record = paymentRecordDAO.getRecordById(id);
        if (record == null) {
            return false;
        }

        // 获取账单
        PaymentBill bill = paymentBillDAO.getBillById(record.getBillId());
        if (bill == null) {
            return false;
        }

        // 更新账单已缴金额和状态
        BigDecimal newPaidAmount = bill.getPaidAmount().subtract(record.getPaymentAmount());
        if (newPaidAmount.compareTo(BigDecimal.ZERO) < 0) {
            newPaidAmount = BigDecimal.ZERO;
        }

        String newStatus;
        if (newPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
            newStatus = "未缴费";
        } else if (newPaidAmount.compareTo(bill.getAmount()) >= 0) {
            newStatus = "已缴费";
        } else {
            newStatus = "部分缴费";
        }

        // 逾期检查功能已移除

        // 更新账单
        boolean updateBillResult = paymentBillDAO.updateBillPayment(bill.getId(), newPaidAmount, newStatus);
        if (!updateBillResult) {
            return false;
        }

        // 删除缴费记录
        return paymentRecordDAO.deleteRecord(id);
    }

    // 缴费统计功能已移除
}
