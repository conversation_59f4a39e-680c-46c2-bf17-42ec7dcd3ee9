package com.property.util;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Insets;
import java.awt.RenderingHints;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.border.Border;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumn;

/**
 * GUI工具类
 * 提供通用的GUI辅助方法
 */
public class GUIUtil {

    /**
     * 设置表格样式
     * @param table 表格
     */
    public static void setTableStyle(JTable table) {
        // 设置表头样式
        JTableHeader header = table.getTableHeader();
        header.setBackground(UIStyleUtil.PRIMARY_COLOR);
        header.setForeground(Color.WHITE);
        header.setFont(new Font("微软雅黑", Font.BOLD, 14));
        header.setPreferredSize(new Dimension(header.getWidth(), 35));

        // 设置表格行高
        table.setRowHeight(30);

        // 设置表格字体
        table.setFont(new Font("微软雅黑", Font.PLAIN, 14));

        // 设置表格选中行颜色
        table.setSelectionBackground(new Color(230, 230, 250));
        table.setSelectionForeground(Color.BLACK);

        // 设置表格网格线颜色
        table.setGridColor(new Color(240, 240, 240));

        // 设置表格条纹效果
        table.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component comp = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (!isSelected) {
                    if (row % 2 == 0) {
                        comp.setBackground(Color.WHITE);
                    } else {
                        comp.setBackground(new Color(245, 245, 250));
                    }
                }

                // 居中显示
                ((DefaultTableCellRenderer) comp).setHorizontalAlignment(JLabel.CENTER);

                return comp;
            }
        });
    }

    /**
     * 设置表格列宽
     * @param table 表格
     * @param columnWidths 列宽数组
     */
    public static void setColumnWidths(JTable table, int[] columnWidths) {
        for (int i = 0; i < columnWidths.length && i < table.getColumnCount(); i++) {
            TableColumn column = table.getColumnModel().getColumn(i);
            column.setPreferredWidth(columnWidths[i]);
        }
    }

    /**
     * 设置按钮样式
     * @param button 按钮
     * @param backgroundColor 背景颜色
     */
    public static void setButtonStyle(JButton button, Color backgroundColor) {
        button.setBackground(backgroundColor);
        button.setForeground(Color.WHITE);
        button.setFont(new Font("微软雅黑", Font.BOLD, 14));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(button.getPreferredSize().width, 35));
    }

    /**
     * 设置文本框样式
     * @param textField 文本框
     */
    public static void setTextFieldStyle(JTextField textField) {
        textField.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        textField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(200, 200, 200)),
                BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        textField.setPreferredSize(new Dimension(textField.getPreferredSize().width, 35));

        // 添加焦点监听器以实现输入框高亮效果
        textField.addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                textField.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(UIStyleUtil.ACCENT_COLOR),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
            }

            @Override
            public void focusLost(FocusEvent e) {
                textField.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(200, 200, 200)),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
            }
        });
    }

    /**
     * 设置下拉框样式
     * @param comboBox 下拉框
     */
    public static void setComboBoxStyle(JComboBox<?> comboBox) {
        comboBox.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        comboBox.setBackground(Color.WHITE);
        comboBox.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(200, 200, 200)),
                BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        comboBox.setPreferredSize(new Dimension(comboBox.getPreferredSize().width, 35));
    }

    /**
     * 设置标签样式
     * @param label 标签
     */
    public static void setLabelStyle(JLabel label) {
        label.setFont(new Font("微软雅黑", Font.BOLD, 14));
        label.setForeground(UIStyleUtil.TEXT_COLOR);
    }

    /**
     * 格式化日期
     * @param date 日期
     * @param pattern 格式模式
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 圆角面板
     */
    public static class RoundedPanel extends JPanel {
        private int radius;

        public RoundedPanel(int radius) {
            super();
            this.radius = radius;
            setOpaque(false);
        }

        @Override
        protected void paintComponent(Graphics g) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(getBackground());
            g2d.fillRoundRect(0, 0, getWidth(), getHeight(), radius, radius);
            g2d.dispose();
        }
    }

    /**
     * 圆角边框
     */
    public static class RoundedBorder implements Border {
        private int radius;
        private Color color;

        public RoundedBorder(int radius, Color color) {
            this.radius = radius;
            this.color = color;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(color);
            g2d.drawRoundRect(x, y, width - 1, height - 1, radius, radius);
            g2d.dispose();
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(radius / 2, radius / 2, radius / 2, radius / 2);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }
    }

    /**
     * 设置组件边距
     * @param component 组件
     * @param top 上边距
     * @param left 左边距
     * @param bottom 下边距
     * @param right 右边距
     */
    public static void setPadding(JComponent component, int top, int left, int bottom, int right) {
        component.setBorder(BorderFactory.createEmptyBorder(top, left, bottom, right));
    }
}
