package com.property.util;

import java.sql.*;

/**
 * 密码服务类，用于处理密码相关操作
 */
public class PasswordService {
    private static String userName;
    private static String password;

    /**
     * 用于修改密码的构造函数
     * @param password 新密码
     * @param userName 用户名
     */
    public PasswordService(String password, String userName) {
        super();
        PasswordService.userName = userName;
        PasswordService.password = password;
    }

    /**
     * 更新用户密码
     * @return 是否更新成功
     */
    public boolean updatePassword() {
        try (Connection conn = DriverManager.getConnection(
                DatabaseConfig.getJdbcUrl(),
                DatabaseConfig.getUsername(),
                DatabaseConfig.getPassword())) {
            String sql = "UPDATE t_user_account SET password = ? WHERE user_name = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, password);
            stmt.setString(2, userName);
            int rowsUpdated = stmt.executeUpdate();
            if (rowsUpdated > 0) {
                System.out.println("密码修改成功！");
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    // 为了兼容旧代码，保留以下方法
    public boolean f() {
        return updatePassword();
    }
}
