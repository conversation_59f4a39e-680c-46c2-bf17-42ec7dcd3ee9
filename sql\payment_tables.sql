-- =============================================
-- 物业管理系统缴费管理模块数据库设计
-- 功能：创建缴费管理相关的表结构和初始数据
-- =============================================

-- 删除现有表（如果存在）以避免外键冲突
DROP TABLE IF EXISTS t_payment_record;
DROP TABLE IF EXISTS t_payment_bill;
DROP TABLE IF EXISTS t_fee_type;

-- 创建费用类型表
-- 该表存储不同类型的费用信息，如物业费、水电费、停车费等
CREATE TABLE IF NOT EXISTS t_fee_type (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '费用类型名称',
    description VARCHAR(200) COMMENT '费用描述',
    unit_price DECIMAL(10, 2) NOT NULL COMMENT '单价',
    billing_cycle VARCHAR(20) NOT NULL COMMENT '计费周期：月度、季度、年度',
    calculation_method VARCHAR(20) NOT NULL COMMENT '计费方式：固定金额、按面积、按用量等',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_fee_type_name` (name) COMMENT '费用类型名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='费用类型表';

-- 创建账单表
-- 该表存储生成的账单信息，关联到业主和费用类型
CREATE TABLE IF NOT EXISTS t_payment_bill (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bill_no VARCHAR(50) NOT NULL COMMENT '账单编号',
    owner_id INT NOT NULL COMMENT '业主ID',
    fee_type_id INT NOT NULL COMMENT '费用类型ID',
    billing_period VARCHAR(50) NOT NULL COMMENT '账单周期（如：2023年1月）',
    amount DECIMAL(10, 2) NOT NULL COMMENT '应缴金额',
    paid_amount DECIMAL(10, 2) DEFAULT 0 COMMENT '已缴金额',
    status VARCHAR(20) NOT NULL DEFAULT '未缴费' COMMENT '状态：未缴费、部分缴费、已缴费',
    due_date DATE NOT NULL COMMENT '缴费截止日期',
    remark VARCHAR(200) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_bill_no` (bill_no) COMMENT '账单编号唯一索引',
    CONSTRAINT `fk_bill_owner` FOREIGN KEY (owner_id) REFERENCES t_owner_info (id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_bill_fee_type` FOREIGN KEY (fee_type_id) REFERENCES t_fee_type (id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单表';

-- 创建缴费记录表
-- 该表存储业主的缴费记录，关联到账单
CREATE TABLE IF NOT EXISTS t_payment_record (
    id INT AUTO_INCREMENT PRIMARY KEY,
    record_no VARCHAR(50) NOT NULL COMMENT '缴费记录编号',
    bill_id INT NOT NULL COMMENT '关联的账单ID',
    owner_id INT NOT NULL COMMENT '业主ID',
    payment_amount DECIMAL(10, 2) NOT NULL COMMENT '缴费金额',
    payment_method VARCHAR(20) NOT NULL COMMENT '缴费方式：现金、银行转账、在线支付等',
    payment_time DATETIME NOT NULL COMMENT '缴费时间',
    operator VARCHAR(50) COMMENT '操作员',
    receipt_no VARCHAR(50) COMMENT '收据编号',
    remark VARCHAR(200) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_record_no` (record_no) COMMENT '缴费记录编号唯一索引',
    CONSTRAINT `fk_record_bill` FOREIGN KEY (bill_id) REFERENCES t_payment_bill (id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT `fk_record_owner` FOREIGN KEY (owner_id) REFERENCES t_owner_info (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缴费记录表';

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入费用类型数据
INSERT INTO t_fee_type (name, description, unit_price, billing_cycle, calculation_method, is_active) VALUES
('物业管理费', '基础物业服务费用', 2.50, '月度', '按面积', 1),
('停车费', '小区内车位停放费用', 150.00, '月度', '固定金额', 1),
('水费', '自来水使用费用', 3.80, '月度', '按用量', 1),
('电费', '电力使用费用', 0.55, '月度', '按用量', 1),
('垃圾处理费', '生活垃圾处理费用', 10.00, '月度', '固定金额', 1);

-- =============================================
-- 数据库设置完成
-- =============================================
