-- =============================================
-- 物业管理系统数据库设置脚本
-- 功能：创建数据库、表结构和初始数据
-- 特点：实现业主信息表与其他表的外键关联，支持级联删除
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS mysql_assignment DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE mysql_assignment;

-- 删除现有表（如果存在）以避免外键冲突
-- 注意：必须先删除有外键约束的表，再删除被引用的表
DROP TABLE IF EXISTS t_repair_request;  -- 报修请求表（引用业主表）
DROP TABLE IF EXISTS t_user_account;    -- 用户账户表（引用业主表）
DROP TABLE IF EXISTS t_admin_account;   -- 管理员账户表
DROP TABLE IF EXISTS t_owner_info;      -- 业主信息表（被引用表）

-- 创建业主信息表（核心表，被其他表引用）
-- 该表存储所有业主的基本信息，是系统的核心表
CREATE TABLE IF NOT EXISTS t_owner_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '业主姓名',
    gender VARCHAR(10) COMMENT '性别',
    house_number VARCHAR(50) COMMENT '门牌号',
    phone VARCHAR(20) COMMENT '联系电话',
    id_number VARCHAR(50) COMMENT '身份证号',
    remark VARCHAR(200) COMMENT '备注',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_name` (name) COMMENT '业主姓名唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业主信息表';

-- 创建用户账户表（关联到业主信息表）
-- 该表存储业主的登录账户信息，通过外键与业主信息表关联
-- 当删除业主信息时，对应的用户账户也会被级联删除
CREATE TABLE IF NOT EXISTS t_user_account (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(50) NOT NULL COMMENT '密码',
    owner_id INT NOT NULL COMMENT '关联的业主ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_user_name` (user_name) COMMENT '用户名唯一索引',
    CONSTRAINT `fk_user_owner` FOREIGN KEY (owner_id) REFERENCES t_owner_info (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户账户表';

-- 创建报修请求表（关联到业主信息表）
-- 该表存储业主的报修请求信息，通过外键与业主信息表关联
-- 当删除业主信息时，对应的报修请求也会被级联删除
CREATE TABLE IF NOT EXISTS t_repair_request (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '业主姓名',
    house_number VARCHAR(50) COMMENT '门牌号',
    repair_content TEXT COMMENT '报修内容',
    status VARCHAR(20) DEFAULT '待处理' COMMENT '处理状态：待处理、处理中、已完成',
    owner_id INT NOT NULL COMMENT '关联的业主ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    CONSTRAINT `fk_repair_owner` FOREIGN KEY (owner_id) REFERENCES t_owner_info (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报修请求表';

-- 创建管理员账户表（独立表，不关联业主）
-- 该表存储系统管理员的账户信息，与业主信息表无关联
CREATE TABLE IF NOT EXISTS t_admin_account (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(50) NOT NULL COMMENT '管理员用户名',
    password VARCHAR(50) NOT NULL COMMENT '管理员密码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_admin_user_name` (user_name) COMMENT '管理员用户名唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员账户表';

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入初始管理员账户
INSERT INTO t_admin_account (user_name, password) VALUES ('admin', 'admin');

-- 插入测试数据：业主信息
-- 注意：业主姓名设置了唯一索引，不能重复
INSERT INTO t_owner_info (name, gender, house_number, phone, id_number, remark, emergency_contact, emergency_phone) VALUES
('张三', '男', 'A-101', '***********', '110101199001011234', '长期租住', '张小三', '***********'),
('李四', '女', 'A-102', '***********', '110101199001021234', '业主代表', '李小四', '***********'),
('王五', '男', 'B-201', '***********', '110101199001031234', '经常出差', '王小五', '***********'),
('赵六', '女', 'B-202', '***********', '110101199001041234', '独居老人', '赵小六', '***********'),
('孙七', '男', 'C-301', '***********', '110101199001051234', '有宠物犬', '孙小七', '***********'),
('周八', '女', 'C-302', '***********', '110101199001061234', '需要特别关注', '周小八', '***********'),
('吴九', '男', 'D-401', '***********', '110101199001071234', '装修中', '吴小九', '***********'),
('郑十', '女', 'D-402', '***********', '110101199001081234', '新入住', '郑小十', '***********');

-- 插入测试数据：用户账户（关联到业主信息）
-- 通过SELECT语句从业主表获取ID，确保外键关系正确
-- 用户名与业主姓名相同，密码统一设置为123456
INSERT INTO t_user_account (user_name, password, owner_id)
SELECT name, '123456', id FROM t_owner_info WHERE name IN ('张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十');

-- 插入测试数据：报修请求（关联到业主信息）
-- 为每个业主插入报修请求，通过SELECT语句从业主表获取ID和门牌号
-- 确保外键关系正确，实现级联删除
INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '厨房水管漏水', '待处理', id FROM t_owner_info WHERE name = '张三';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '卧室灯不亮', '处理中', id FROM t_owner_info WHERE name = '李四';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '卫生间马桶堵塞', '已完成', id FROM t_owner_info WHERE name = '王五';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '阳台门把手损坏', '待处理', id FROM t_owner_info WHERE name = '赵六';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '空调不制冷', '处理中', id FROM t_owner_info WHERE name = '孙七';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '燃气灶打不着火', '待处理', id FROM t_owner_info WHERE name = '周八';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '热水器漏水', '已完成', id FROM t_owner_info WHERE name = '吴九';

INSERT INTO t_repair_request (name, house_number, repair_content, status, owner_id)
SELECT name, house_number, '门锁需要更换', '处理中', id FROM t_owner_info WHERE name = '郑十';

-- =============================================
-- 数据库设置完成
-- 注意：删除业主信息时，相关的用户账户和报修请求会自动级联删除
-- =============================================

