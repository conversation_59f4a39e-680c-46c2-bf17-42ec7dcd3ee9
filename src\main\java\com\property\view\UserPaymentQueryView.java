package com.property.view;

import com.property.model.PaymentBill;
import com.property.service.PaymentService;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 用户缴费查询界面
 * 专门为普通用户设计的缴费查询界面，只能查看自己的缴费信息
 */
public class UserPaymentQueryView extends JFrame {
    private PaymentService paymentService;
    private JTable billTable;
    private DefaultTableModel tableModel;
    private JButton viewButton;
    private JButton paymentButton; // 添加缴费按钮
    private JButton refreshButton;

    public UserPaymentQueryView() {
        paymentService = new PaymentService();
        initComponents();
        loadUserBills();
    }

    private void initComponents() {
        setTitle("我的缴费信息");
        setSize(900, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);

        // 创建标题栏
        JPanel titleBar = new JPanel();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setPreferredSize(new Dimension(900, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));

        // 添加系统标题
        JLabel titleLabel = new JLabel("我的缴费信息");
        titleLabel.setFont(UIStyleUtil.MENU_FONT);
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 0));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建表格模型
        String[] columnNames = {"ID", "账单编号", "费用类型", "账单周期", "应缴金额", "已缴金额", "状态", "截止日期"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 设置表格不可编辑
            }
        };
        billTable = new JTable(tableModel);
        billTable.getTableHeader().setReorderingAllowed(false);
        billTable.getTableHeader().setResizingAllowed(true);
        billTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        billTable.setRowHeight(25);

        // 设置表格列宽
        billTable.getColumnModel().getColumn(0).setPreferredWidth(50);
        billTable.getColumnModel().getColumn(1).setPreferredWidth(120);
        billTable.getColumnModel().getColumn(2).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(4).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(5).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(6).setPreferredWidth(80);
        billTable.getColumnModel().getColumn(7).setPreferredWidth(100);

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(billTable);

        // 创建标题面板
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titlePanel.setPreferredSize(new Dimension(900, 60));

        JLabel panelTitleLabel = new JLabel("我的缴费信息", JLabel.CENTER);
        panelTitleLabel.setFont(UIStyleUtil.TITLE_FONT);
        panelTitleLabel.setForeground(UIStyleUtil.TITLE_COLOR);
        titlePanel.add(panelTitleLabel, BorderLayout.CENTER);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        viewButton = new UIStyleUtil.RoundedButton("查看详情", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        paymentButton = new UIStyleUtil.RoundedButton("缴费", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        refreshButton = new UIStyleUtil.RoundedButton("刷新", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        buttonPanel.add(viewButton);
        buttonPanel.add(paymentButton);
        buttonPanel.add(refreshButton);

        // 创建内容面板
        JPanel mainPanel = new UIStyleUtil.ShadowPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 设置表格样式
        billTable.getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 14));
        billTable.getTableHeader().setBackground(UIStyleUtil.PRIMARY_COLOR);
        billTable.getTableHeader().setForeground(Color.WHITE);

        // 设置表格条纹效果
        billTable.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (isSelected) {
                    c.setBackground(new Color(232, 242, 254));
                    c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                } else {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 247, 250));
                    }
                    c.setForeground(UIStyleUtil.TEXT_COLOR);
                }

                ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                ((JLabel) c).setHorizontalAlignment(column == 0 ? JLabel.CENTER : JLabel.LEFT);

                return c;
            }
        });

        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        // 创建北部面板，包含标题栏和顶部面板
        JPanel northPanel = new JPanel(new BorderLayout());
        northPanel.setOpaque(false);
        northPanel.add(titleBar, BorderLayout.NORTH);
        northPanel.add(titlePanel, BorderLayout.SOUTH);

        // 设置布局
        setLayout(new BorderLayout());
        add(northPanel, BorderLayout.NORTH);
        add(mainPanel, BorderLayout.CENTER);

        // 添加事件监听器
        viewButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = billTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) billTable.getValueAt(selectedRow, 0);
                    showBillDetailDialog(id);
                } else {
                    DialogUtil.showWarningDialog(UserPaymentQueryView.this, "请先选择一个账单");
                }
            }
        });

        paymentButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = billTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) billTable.getValueAt(selectedRow, 0);
                    String status = (String) billTable.getValueAt(selectedRow, 6);
                    if ("已缴费".equals(status)) {
                        DialogUtil.showWarningDialog(UserPaymentQueryView.this, "该账单已缴清，无需再次缴费");
                    } else {
                        showPaymentDialog(id);
                    }
                } else {
                    DialogUtil.showWarningDialog(UserPaymentQueryView.this, "请先选择一个账单");
                }
            }
        });

        refreshButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                loadUserBills();
            }
        });
    }

    /**
     * 加载当前用户的账单
     */
    private void loadUserBills() {
        // 清空表格
        tableModel.setRowCount(0);

        // 获取当前用户的账单
        List<PaymentBill> bills;

        // 如果当前用户名不为空，则查询该用户的账单
        if (LoginView.currentUserName != null && !LoginView.currentUserName.isEmpty()) {
            // 根据用户名查询业主ID
            int ownerId = getOwnerIdByUsername(LoginView.currentUserName);
            if (ownerId > 0) {
                bills = paymentService.getBillsByOwnerId(ownerId);
            } else {
                // 如果找不到业主ID，则显示所有账单（仅用于测试）
                bills = paymentService.getAllBills();
            }
        } else {
            // 如果当前用户名为空，则显示所有账单（仅用于测试）
            bills = paymentService.getAllBills();
        }

        // 填充表格
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (PaymentBill bill : bills) {
            Object[] rowData = {
                bill.getId(),
                bill.getBillNo(),
                bill.getFeeTypeName(),
                bill.getBillingPeriod(),
                bill.getAmount(),
                bill.getPaidAmount(),
                bill.getStatus(),
                dateFormat.format(bill.getDueDate())
            };
            tableModel.addRow(rowData);
        }
    }

    /**
     * 根据用户名获取业主ID
     * @param username 用户名
     * @return 业主ID，如果找不到则返回-1
     */
    private int getOwnerIdByUsername(String username) {
        // 这里应该查询数据库，根据用户名获取业主ID
        // 暂时使用模拟数据，假设用户名就是业主姓名
        try {
            // 查询业主信息表，根据姓名获取ID
            java.sql.Connection conn = new com.property.util.DatabaseConnection().getConnection();
            java.sql.PreparedStatement stmt = conn.prepareStatement(
                "SELECT id FROM t_owner_info WHERE name = ?"
            );
            stmt.setString(1, username);
            java.sql.ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return rs.getInt("id");
            }

            rs.close();
            stmt.close();
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return -1;
    }

    /**
     * 显示账单详情对话框
     * @param id 账单ID
     */
    private void showBillDetailDialog(int id) {
        PaymentBill bill = paymentService.getBillById(id);
        if (bill == null) {
            DialogUtil.showErrorDialog(this, "获取账单信息失败");
            return;
        }

        JDialog dialog = new JDialog(this, "账单详情", true);
        dialog.setSize(500, 400);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        // 设置对话框背景
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        dialog.setContentPane(contentPane);

        // 账单信息面板
        JPanel billInfoPanel = new UIStyleUtil.ShadowPanel();
        billInfoPanel.setLayout(new GridLayout(8, 2, 10, 10));
        billInfoPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(UIStyleUtil.PRIMARY_COLOR, 1, true),
            "账单信息",
            TitledBorder.DEFAULT_JUSTIFICATION,
            TitledBorder.DEFAULT_POSITION,
            UIStyleUtil.LABEL_FONT,
            UIStyleUtil.PRIMARY_COLOR));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        billInfoPanel.add(new JLabel("账单编号:"));
        billInfoPanel.add(new JLabel(bill.getBillNo()));
        billInfoPanel.add(new JLabel("费用类型:"));
        billInfoPanel.add(new JLabel(bill.getFeeTypeName()));
        billInfoPanel.add(new JLabel("账单周期:"));
        billInfoPanel.add(new JLabel(bill.getBillingPeriod()));
        billInfoPanel.add(new JLabel("应缴金额:"));
        billInfoPanel.add(new JLabel(bill.getAmount().toString()));
        billInfoPanel.add(new JLabel("已缴金额:"));
        billInfoPanel.add(new JLabel(bill.getPaidAmount().toString()));
        billInfoPanel.add(new JLabel("状态:"));
        billInfoPanel.add(new JLabel(bill.getStatus()));
        billInfoPanel.add(new JLabel("截止日期:"));
        billInfoPanel.add(new JLabel(dateFormat.format(bill.getDueDate())));
        billInfoPanel.add(new JLabel("备注:"));
        billInfoPanel.add(new JLabel(bill.getRemark() != null ? bill.getRemark() : ""));

        // 缴费记录面板
        JPanel recordPanel = new UIStyleUtil.ShadowPanel();
        recordPanel.setLayout(new BorderLayout());
        recordPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(UIStyleUtil.SECONDARY_COLOR, 1, true),
            "缴费记录",
            TitledBorder.DEFAULT_JUSTIFICATION,
            TitledBorder.DEFAULT_POSITION,
            UIStyleUtil.LABEL_FONT,
            UIStyleUtil.SECONDARY_COLOR));

        String[] recordColumnNames = {"记录编号", "缴费金额", "缴费方式", "缴费时间", "收据编号"};
        DefaultTableModel recordTableModel = new DefaultTableModel(recordColumnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        JTable recordTable = new JTable(recordTableModel);
        recordTable.getTableHeader().setReorderingAllowed(false);
        recordTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        recordTable.setRowHeight(25);

        // 设置表格样式
        recordTable.getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 14));
        recordTable.getTableHeader().setBackground(UIStyleUtil.PRIMARY_COLOR);
        recordTable.getTableHeader().setForeground(Color.WHITE);

        // 设置表格条纹效果
        recordTable.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (isSelected) {
                    c.setBackground(new Color(232, 242, 254));
                    c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                } else {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 247, 250));
                    }
                    c.setForeground(UIStyleUtil.TEXT_COLOR);
                }

                ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                ((JLabel) c).setHorizontalAlignment(column == 0 ? JLabel.CENTER : JLabel.LEFT);

                return c;
            }
        });

        JScrollPane recordScrollPane = new JScrollPane(recordTable);
        recordPanel.add(recordScrollPane, BorderLayout.CENTER);

        // 加载缴费记录
        List<com.property.model.PaymentRecord> records = paymentService.getRecordsByBillId(id);
        SimpleDateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (com.property.model.PaymentRecord record : records) {
            Object[] rowData = {
                record.getRecordNo(),
                record.getPaymentAmount(),
                record.getPaymentMethod(),
                datetimeFormat.format(record.getPaymentTime()),
                record.getReceiptNo()
            };
            recordTableModel.addRow(rowData);
        }

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        JButton payButton = new UIStyleUtil.RoundedButton("缴费", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        JButton closeButton = new UIStyleUtil.RoundedButton("关闭", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        // 如果账单已缴清，则禁用缴费按钮
        if ("已缴费".equals(bill.getStatus())) {
            payButton.setEnabled(false);
            payButton.setToolTipText("该账单已缴清");
        }

        buttonPanel.add(payButton);
        buttonPanel.add(closeButton);

        // 设置布局
        JPanel mainPanel = new JPanel(new BorderLayout(0, 10));
        mainPanel.setOpaque(false);
        mainPanel.add(billInfoPanel, BorderLayout.NORTH);
        mainPanel.add(recordPanel, BorderLayout.CENTER);

        dialog.add(mainPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        payButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
                showPaymentDialog(id);
            }
        });

        closeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 显示缴费对话框
     * @param id 账单ID
     */
    private void showPaymentDialog(int id) {
        PaymentBill bill = paymentService.getBillById(id);
        if (bill == null) {
            DialogUtil.showErrorDialog(this, "获取账单信息失败");
            return;
        }

        JDialog dialog = new JDialog(this, "缴费处理", true);
        dialog.setSize(500, 400);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        // 设置对话框背景
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        dialog.setContentPane(contentPane);

        // 账单信息面板
        JPanel billInfoPanel = new UIStyleUtil.ShadowPanel();
        billInfoPanel.setLayout(new GridLayout(5, 2, 10, 10));
        billInfoPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(UIStyleUtil.PRIMARY_COLOR, 1, true),
            "账单信息",
            TitledBorder.DEFAULT_JUSTIFICATION,
            TitledBorder.DEFAULT_POSITION,
            UIStyleUtil.LABEL_FONT,
            UIStyleUtil.PRIMARY_COLOR));

        billInfoPanel.add(new JLabel("账单编号:"));
        billInfoPanel.add(new JLabel(bill.getBillNo()));
        billInfoPanel.add(new JLabel("费用类型:"));
        billInfoPanel.add(new JLabel(bill.getFeeTypeName()));
        billInfoPanel.add(new JLabel("应缴金额:"));
        billInfoPanel.add(new JLabel(bill.getAmount().toString()));
        billInfoPanel.add(new JLabel("已缴金额:"));
        billInfoPanel.add(new JLabel(bill.getPaidAmount().toString()));
        billInfoPanel.add(new JLabel("状态:"));
        billInfoPanel.add(new JLabel(bill.getStatus()));

        // 缴费表单面板
        JPanel formPanel = new UIStyleUtil.ShadowPanel();
        formPanel.setLayout(new GridLayout(4, 2, 10, 10));
        formPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(UIStyleUtil.SECONDARY_COLOR, 1, true),
            "缴费信息",
            TitledBorder.DEFAULT_JUSTIFICATION,
            TitledBorder.DEFAULT_POSITION,
            UIStyleUtil.LABEL_FONT,
            UIStyleUtil.SECONDARY_COLOR));

        JTextField paymentAmountField = new JTextField();
        UIStyleUtil.styleTextField(paymentAmountField);
        BigDecimal remainingAmount = bill.getAmount().subtract(bill.getPaidAmount());
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            paymentAmountField.setText(remainingAmount.toString());
        }

        String[] paymentMethods = {"现金", "银行转账", "在线支付", "支付宝", "微信支付"};
        JComboBox<String> paymentMethodComboBox = new JComboBox<>(paymentMethods);
        paymentMethodComboBox.setFont(UIStyleUtil.LABEL_FONT);
        paymentMethodComboBox.setBackground(Color.WHITE);

        JTextField remarkField = new JTextField();
        UIStyleUtil.styleTextField(remarkField);

        JLabel amountLabel = new JLabel("缴费金额:");
        amountLabel.setFont(UIStyleUtil.LABEL_FONT);
        amountLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        JLabel methodLabel = new JLabel("缴费方式:");
        methodLabel.setFont(UIStyleUtil.LABEL_FONT);
        methodLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        JLabel remarkLabel = new JLabel("备注:");
        remarkLabel.setFont(UIStyleUtil.LABEL_FONT);
        remarkLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        formPanel.add(amountLabel);
        formPanel.add(paymentAmountField);
        formPanel.add(methodLabel);
        formPanel.add(paymentMethodComboBox);
        formPanel.add(remarkLabel);
        formPanel.add(remarkField);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        JButton confirmButton = new UIStyleUtil.RoundedButton("确认缴费", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        JButton cancelButton = new UIStyleUtil.RoundedButton("取消", new Color(231, 76, 60), UIStyleUtil.SECONDARY_COLOR);

        buttonPanel.add(confirmButton);
        buttonPanel.add(cancelButton);

        // 设置布局
        JPanel mainPanel = new JPanel(new BorderLayout(0, 10));
        mainPanel.setOpaque(false);
        mainPanel.add(billInfoPanel, BorderLayout.NORTH);
        mainPanel.add(formPanel, BorderLayout.CENTER);

        dialog.add(mainPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        confirmButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 验证输入
                String paymentAmountText = paymentAmountField.getText().trim();
                String paymentMethod = (String) paymentMethodComboBox.getSelectedItem();
                String remark = remarkField.getText().trim();

                if (paymentAmountText.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入缴费金额");
                    return;
                }

                BigDecimal paymentAmount;
                try {
                    paymentAmount = new BigDecimal(paymentAmountText);
                } catch (NumberFormatException ex) {
                    DialogUtil.showWarningDialog(dialog, "缴费金额必须是有效的数字");
                    return;
                }

                if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    DialogUtil.showWarningDialog(dialog, "缴费金额必须大于0");
                    return;
                }

                BigDecimal remainingAmount = bill.getAmount().subtract(bill.getPaidAmount());
                if (paymentAmount.compareTo(remainingAmount) > 0) {
                    DialogUtil.showWarningDialog(dialog, "缴费金额不能超过剩余应缴金额: " + remainingAmount);
                    return;
                }

                // 处理缴费
                boolean success = paymentService.processPayment(bill.getId(), paymentAmount, paymentMethod,
                                                              LoginView.currentUserName, remark);

                if (success) {
                    DialogUtil.showInfoDialog(dialog, "缴费成功");
                    dialog.dispose();
                    loadUserBills(); // 刷新账单列表
                } else {
                    DialogUtil.showErrorDialog(dialog, "缴费失败");
                }
            }
        });

        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                new UserPaymentQueryView().setVisible(true);
            }
        });
    }
}
