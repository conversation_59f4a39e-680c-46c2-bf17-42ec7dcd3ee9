package com.property;
import javax.swing.SwingUtilities;
import com.property.view.LoginView;
/**
 * 物业管理系统入口类
 * 用于启动整个应用程序
 */
public class app {
    /**
     * 应用程序主入口
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            public void run() {
                try {
                    // 创建并显示登录界面
                    LoginView loginView = new LoginView();
                    loginView.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }
}
