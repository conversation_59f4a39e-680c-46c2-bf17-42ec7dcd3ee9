package com.property.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.EventQueue;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuBar;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.border.LineBorder;

import com.property.dao.OwnerDao;
import com.property.model.Owner;
import com.property.util.DatabaseConnection;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.GradientPanel;
import com.property.util.UIStyleUtil.RoundedButton;
import com.property.util.UIStyleUtil.ShadowPanel;

/**
 * 报修服务界面
 */
public class RepairServiceView extends JFrame {

    private static final long serialVersionUID = 1L;
    private GradientPanel contentPane;
    private JTextField nameField;
    private JTextField houseNumberField;
    private JTextArea repairContentField;
    private RoundedButton submitButton;
    private RoundedButton clearButton;

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    RepairServiceView frame = new RepairServiceView();
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建报修服务界面
     */
    public RepairServiceView() {
        // 设置窗口类型为弹出式
        setFont(new Font("Dialog", Font.PLAIN, 15));
        setTitle("保修服务");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 600, 500);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        contentPane = new GradientPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);
        contentPane.setLayout(new BorderLayout());

        // 创建现代化菜单栏作为标题栏
        JMenuBar titleBar = new JMenuBar();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setBorder(BorderFactory.createEmptyBorder());
        titleBar.setPreferredSize(new Dimension(600, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));
        contentPane.add(titleBar, BorderLayout.NORTH);

        // 添加系统标题
        JLabel titleLabel = new JLabel("维修与保修服务");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 40));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建内容面板
        ShadowPanel contentPanel = new ShadowPanel();
        contentPanel.setLayout(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        contentPane.add(contentPanel, BorderLayout.CENTER);

        // 创建表单面板
        JPanel formPanel = new JPanel();
        formPanel.setOpaque(false);
        formPanel.setLayout(null);
        contentPanel.add(formPanel, BorderLayout.CENTER);

        // 创建表单标题
        JLabel formTitleLabel = new JLabel("请填写保修信息");
        formTitleLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        formTitleLabel.setForeground(UIStyleUtil.PRIMARY_COLOR);
        formTitleLabel.setBounds(20, 10, 200, 30);
        formPanel.add(formTitleLabel);

        // 业主姓名标签
        JLabel nameLabel = new JLabel("业主姓名：");
        nameLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        nameLabel.setBounds(20, 60, 80, 30);
        formPanel.add(nameLabel);

        // 业主姓名输入框
        nameField = new JTextField();
        nameField.setBounds(100, 60, 150, 30);
        UIStyleUtil.styleTextField(nameField);
        formPanel.add(nameField);

        // 门牌号标签
        JLabel houseNumberLabel = new JLabel("门牌号：");
        houseNumberLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        houseNumberLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        houseNumberLabel.setBounds(280, 60, 80, 30);
        formPanel.add(houseNumberLabel);

        // 门牌号输入框
        houseNumberField = new JTextField();
        houseNumberField.setBounds(350, 60, 150, 30);
        UIStyleUtil.styleTextField(houseNumberField);
        formPanel.add(houseNumberField);

        // 保修内容标签
        JLabel repairContentLabel = new JLabel("保修内容：");
        repairContentLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        repairContentLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        repairContentLabel.setBounds(20, 110, 80, 30);
        formPanel.add(repairContentLabel);

        // 保修内容文本区
        repairContentField = new JTextArea();
        repairContentField.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        repairContentField.setLineWrap(true);
        repairContentField.setWrapStyleWord(true);
        repairContentField.setBorder(BorderFactory.createCompoundBorder(
                new LineBorder(new Color(200, 200, 200)),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)));

        // 添加滚动面板
        JScrollPane scrollPane = new JScrollPane(repairContentField);
        scrollPane.setBounds(100, 110, 400, 150);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
                new LineBorder(new Color(200, 200, 200)),
                BorderFactory.createEmptyBorder(0, 0, 0, 0)));
        formPanel.add(scrollPane);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel();
        buttonPanel.setOpaque(false);
        buttonPanel.setBounds(0, 280, 550, 50);
        buttonPanel.setLayout(new BoxLayout(buttonPanel, BoxLayout.X_AXIS));
        formPanel.add(buttonPanel);

        // 添加弹性空间
        buttonPanel.add(Box.createHorizontalGlue());

        // 提交按钮
        submitButton = new RoundedButton("提交保修请求", UIStyleUtil.PRIMARY_COLOR, UIStyleUtil.SECONDARY_COLOR);
        submitButton.setFont(new Font("微软雅黑", Font.BOLD, 14));
        submitButton.setPreferredSize(new Dimension(150, 40));
        submitButton.setMaximumSize(new Dimension(150, 40));
        submitButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                submitRepairRequest();
            }
        });
        buttonPanel.add(submitButton);

        // 添加间距
        buttonPanel.add(Box.createRigidArea(new Dimension(20, 0)));

        // 清除按钮
        clearButton = new RoundedButton("清除内容", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        clearButton.setFont(new Font("微软雅黑", Font.BOLD, 14));
        clearButton.setPreferredSize(new Dimension(120, 40));
        clearButton.setMaximumSize(new Dimension(120, 40));
        clearButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                nameField.setText("");
                houseNumberField.setText("");
                repairContentField.setText("");
            }
        });
        buttonPanel.add(clearButton);

        // 添加弹性空间
        buttonPanel.add(Box.createHorizontalGlue());
    }

    /**
     * 提交报修请求
     */
    private void submitRepairRequest() {
        // 获取输入信息
        String name = nameField.getText().trim();
        String houseNumber = houseNumberField.getText().trim();
        String repairContent = repairContentField.getText().trim();

        // 输入验证
        if (name.isEmpty()) {
            DialogUtil.showWarning(this, "请输入业主姓名");
            nameField.requestFocus();
            return;
        }

        if (houseNumber.isEmpty()) {
            DialogUtil.showWarning(this, "请输入门牌号");
            houseNumberField.requestFocus();
            return;
        }

        if (repairContent.isEmpty()) {
            DialogUtil.showWarning(this, "请输入保修内容");
            repairContentField.requestFocus();
            return;
        }

        // 创建业主对象
        Owner owner = new Owner(name, houseNumber, repairContent);

        // 创建数据库连接
        DatabaseConnection dbConnection = new DatabaseConnection();

        // 创建DAO对象
        OwnerDao ownerDao = new OwnerDao();

        // 连接数据库
        Connection connection = null;
        try {
            connection = dbConnection.getConnection();
            ownerDao.insertRepairRequest(connection, owner);

            // 显示成功提示
            DialogUtil.showSuccess(this, "保修请求提交成功！");

            // 清空输入框
            nameField.setText("");
            houseNumberField.setText("");
            repairContentField.setText("");
        } catch (Exception e) {
            e.printStackTrace();
            DialogUtil.showError(this, "保修请求提交失败: " + e.getMessage());
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
