package com.property.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.EventQueue;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseWheelEvent;
import java.awt.event.MouseWheelListener;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuBar;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.ScrollPaneConstants;
import javax.swing.border.EmptyBorder;

import com.property.util.DialogUtil;
import com.property.util.OwnerDataService;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.GradientPanel;
import com.property.util.UIStyleUtil.RoundedButton;
import com.property.util.UIStyleUtil.ShadowPanel;

/**
 * 添加业主信息界面
 */
public class AddOwnerView extends JFrame {

    private static final long serialVersionUID = 1L;
    private GradientPanel contentPane;
    private JTextField nameField;
    private JComboBox<String> sexComboBox;
    private JTextField houseNumberField;
    private JTextField phoneField;
    private JTextField idNumberField;
    private JTextField remarkField;
    private JTextField emergencyContactField;
    private JTextField emergencyPhoneField;
    private RoundedButton confirmButton;
    private RoundedButton clearButton;
    private static AddOwnerView frame = new AddOwnerView();
    private OwnerInfoView ownerInfoView; // 添加业主信息视图的引用

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建添加业主信息界面（带业主信息视图引用）
     * @param ownerInfoView 业主信息视图
     */
    public AddOwnerView(OwnerInfoView ownerInfoView) {
        this();
        this.ownerInfoView = ownerInfoView;
    }

    /**
     * 创建添加业主信息界面
     */
    public AddOwnerView() {
        setTitle("添加业主信息");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 600, 500);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        contentPane = new GradientPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);
        contentPane.setLayout(new BorderLayout());

        // 创建现代化菜单栏作为标题栏
        JMenuBar titleBar = new JMenuBar();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setBorder(BorderFactory.createEmptyBorder());
        titleBar.setPreferredSize(new Dimension(600, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));
        contentPane.add(titleBar, BorderLayout.NORTH);

        // 添加系统标题
        JLabel titleLabel = new JLabel("添加业主信息");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 40));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建内容面板
        ShadowPanel contentPanel = new ShadowPanel();
        contentPanel.setLayout(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        contentPane.add(contentPanel, BorderLayout.CENTER);

        // 创建表单面板
        JPanel formPanel = new JPanel();
        formPanel.setOpaque(false);
        formPanel.setLayout(null);
        formPanel.setPreferredSize(new Dimension(520, 530)); // 设置面板首选大小，以支持滚动

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(formPanel);
        scrollPane.setOpaque(false);
        scrollPane.getViewport().setOpaque(false);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16); // 设置滚动速度

        // 美化滚动条
        scrollPane.getVerticalScrollBar().setUI(new javax.swing.plaf.basic.BasicScrollBarUI() {
            @Override
            protected void configureScrollBarColors() {
                this.thumbColor = new Color(180, 180, 180, 150); // 滚动条滑块颜色
                this.trackColor = new Color(240, 240, 240, 80);  // 滚动条轨道颜色
            }

            @Override
            protected javax.swing.JButton createDecreaseButton(int orientation) {
                return createZeroButton();
            }

            @Override
            protected javax.swing.JButton createIncreaseButton(int orientation) {
                return createZeroButton();
            }

            private javax.swing.JButton createZeroButton() {
                javax.swing.JButton button = new javax.swing.JButton();
                button.setPreferredSize(new Dimension(0, 0));
                button.setMinimumSize(new Dimension(0, 0));
                button.setMaximumSize(new Dimension(0, 0));
                return button;
            }
        });

        // 添加鼠标滚轮监听器，增强滚动体验
        formPanel.addMouseWheelListener(new MouseWheelListener() {
            @Override
            public void mouseWheelMoved(MouseWheelEvent e) {
                // 将鼠标滚轮事件传递给滚动面板
                scrollPane.getVerticalScrollBar().setValue(
                    scrollPane.getVerticalScrollBar().getValue() + e.getUnitsToScroll() * 5
                );
            }
        });

        contentPanel.add(scrollPane, BorderLayout.CENTER);

        // 创建表单标题面板
        JPanel titlePanel = new JPanel();
        titlePanel.setOpaque(false);
        titlePanel.setBounds(0, 0, 550, 40);
        titlePanel.setLayout(null);
        formPanel.add(titlePanel);

        // 添加图标
        JLabel iconLabel = new JLabel("✉");
        iconLabel.setFont(new Font("微软雅黑", Font.BOLD, 20));
        iconLabel.setForeground(UIStyleUtil.PRIMARY_COLOR);
        iconLabel.setBounds(20, 5, 30, 30);
        titlePanel.add(iconLabel);

        // 添加标题
        JLabel formTitleLabel = new JLabel("请填写业主信息");
        formTitleLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        formTitleLabel.setForeground(UIStyleUtil.PRIMARY_COLOR);
        formTitleLabel.setBounds(50, 5, 200, 30);
        titlePanel.add(formTitleLabel);

        // 添加分隔线
        JPanel separatorPanel = new JPanel();
        separatorPanel.setBackground(new Color(230, 230, 230));
        separatorPanel.setBounds(20, 40, 510, 1);
        formPanel.add(separatorPanel);

        // 姓名标签
        JLabel nameLabel = new JLabel("业主姓名：");
        nameLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        nameLabel.setBounds(50, 60, 80, 30);
        formPanel.add(nameLabel);

        // 姓名输入框
        nameField = new JTextField();
        nameField.setBounds(150, 60, 350, 30);
        UIStyleUtil.styleTextField(nameField);
        nameField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    sexComboBox.requestFocus();
                }
            }
        });
        formPanel.add(nameField);

        // 性别标签
        JLabel sexLabel = new JLabel("性别：");
        sexLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        sexLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        sexLabel.setBounds(50, 110, 80, 30);
        formPanel.add(sexLabel);

        // 性别下拉框
        sexComboBox = new JComboBox<String>();
        sexComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {"男", "女"}));
        sexComboBox.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        sexComboBox.setBounds(150, 110, 350, 30);
        sexComboBox.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    houseNumberField.requestFocus();
                }
            }
        });
        formPanel.add(sexComboBox);

        // 门牌号标签
        JLabel houseNumberLabel = new JLabel("门牌号：");
        houseNumberLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        houseNumberLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        houseNumberLabel.setBounds(50, 160, 80, 30);
        formPanel.add(houseNumberLabel);

        // 门牌号输入框
        houseNumberField = new JTextField();
        houseNumberField.setBounds(150, 160, 350, 30);
        UIStyleUtil.styleTextField(houseNumberField);
        houseNumberField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    phoneField.requestFocus();
                }
            }
        });
        formPanel.add(houseNumberField);

        // 联系电话标签
        JLabel phoneLabel = new JLabel("联系电话：");
        phoneLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        phoneLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        phoneLabel.setBounds(50, 210, 80, 30);
        formPanel.add(phoneLabel);

        // 联系电话输入框
        phoneField = new JTextField();
        phoneField.setBounds(150, 210, 350, 30);
        UIStyleUtil.styleTextField(phoneField);
        phoneField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    idNumberField.requestFocus();
                }
            }
        });
        formPanel.add(phoneField);

        // 身份证号标签
        JLabel idNumberLabel = new JLabel("身份证号：");
        idNumberLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        idNumberLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        idNumberLabel.setBounds(50, 260, 80, 30);
        formPanel.add(idNumberLabel);

        // 身份证号输入框
        idNumberField = new JTextField();
        idNumberField.setBounds(150, 260, 350, 30);
        UIStyleUtil.styleTextField(idNumberField);
        idNumberField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    addOwnerInfo(new ActionEvent(idNumberField, ActionEvent.ACTION_PERFORMED, "Enter"));
                }
            }
        });
        formPanel.add(idNumberField);

        // 添加更多字段，测试滚动功能

        // 备注标签
        JLabel remarkLabel = new JLabel("备注信息：");
        remarkLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        remarkLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        remarkLabel.setBounds(50, 310, 80, 30);
        formPanel.add(remarkLabel);

        // 备注输入框
        remarkField = new JTextField();
        remarkField.setBounds(150, 310, 350, 30);
        UIStyleUtil.styleTextField(remarkField);
        remarkField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    emergencyContactField.requestFocus();
                }
            }
        });
        formPanel.add(remarkField);

        // 紧急联系人标签
        JLabel emergencyContactLabel = new JLabel("紧急联系人：");
        emergencyContactLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        emergencyContactLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        emergencyContactLabel.setBounds(50, 360, 90, 30);
        formPanel.add(emergencyContactLabel);

        // 紧急联系人输入框
        emergencyContactField = new JTextField();
        emergencyContactField.setBounds(150, 360, 350, 30);
        UIStyleUtil.styleTextField(emergencyContactField);
        emergencyContactField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    emergencyPhoneField.requestFocus();
                }
            }
        });
        formPanel.add(emergencyContactField);

        // 紧急联系人电话标签
        JLabel emergencyPhoneLabel = new JLabel("紧急电话：");
        emergencyPhoneLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        emergencyPhoneLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        emergencyPhoneLabel.setBounds(50, 410, 80, 30);
        formPanel.add(emergencyPhoneLabel);

        // 紧急联系人电话输入框
        emergencyPhoneField = new JTextField();
        emergencyPhoneField.setBounds(150, 410, 350, 30);
        UIStyleUtil.styleTextField(emergencyPhoneField);
        emergencyPhoneField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    addOwnerInfo(new ActionEvent(emergencyPhoneField, ActionEvent.ACTION_PERFORMED, "Enter"));
                }
            }
        });
        formPanel.add(emergencyPhoneField);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel();
        buttonPanel.setOpaque(false);
        buttonPanel.setBounds(0, 460, 520, 50);
        buttonPanel.setLayout(new BoxLayout(buttonPanel, BoxLayout.X_AXIS));
        formPanel.add(buttonPanel);

        // 添加弹性空间
        buttonPanel.add(Box.createHorizontalGlue());

        // 确认按钮
        confirmButton = new RoundedButton("确认添加", UIStyleUtil.PRIMARY_COLOR, UIStyleUtil.SECONDARY_COLOR);
        confirmButton.setFont(new Font("微软雅黑", Font.BOLD, 14));
        confirmButton.setPreferredSize(new Dimension(150, 40));
        confirmButton.setMaximumSize(new Dimension(150, 40));
        confirmButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                addOwnerInfo(e);
            }
        });
        buttonPanel.add(confirmButton);

        // 添加间距
        buttonPanel.add(Box.createRigidArea(new Dimension(20, 0)));

        // 清空按钮
        clearButton = new RoundedButton("清空内容", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        clearButton.setFont(new Font("微软雅黑", Font.BOLD, 14));
        clearButton.setPreferredSize(new Dimension(120, 40));
        clearButton.setMaximumSize(new Dimension(120, 40));
        clearButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                nameField.setText("");
                sexComboBox.setSelectedIndex(0);
                houseNumberField.setText("");
                phoneField.setText("");
                idNumberField.setText("");
                remarkField.setText("");
                emergencyContactField.setText("");
                emergencyPhoneField.setText("");
            }
        });
        buttonPanel.add(clearButton);

        // 添加弹性空间
        buttonPanel.add(Box.createHorizontalGlue());

    }

    /**
     * 添加业主信息
     */
    private void addOwnerInfo(ActionEvent e) {
        // 获取输入信息
        String name = nameField.getText().trim();
        String sex = sexComboBox.getSelectedItem().toString();
        String houseNumber = houseNumberField.getText().trim();
        String phone = phoneField.getText().trim();
        String idNumber = idNumberField.getText().trim();
        String remark = remarkField.getText().trim();
        String emergencyContact = emergencyContactField.getText().trim();
        String emergencyPhone = emergencyPhoneField.getText().trim();

        // 输入验证
        if (name.isEmpty()) {
            DialogUtil.showWarning(this, "请输入业主姓名");
            nameField.requestFocus();
            return;
        }

        if (houseNumber.isEmpty()) {
            DialogUtil.showWarning(this, "请输入门牌号");
            houseNumberField.requestFocus();
            return;
        }

        if (phone.isEmpty()) {
            DialogUtil.showWarning(this, "请输入联系电话");
            phoneField.requestFocus();
            return;
        }

        // 简单的电话号码格式验证
        if (!phone.matches("\\d{11}")) {
            DialogUtil.showWarning(this, "请输入正确的手机号码格式（十一位数字）");
            phoneField.requestFocus();
            return;
        }

        if (idNumber.isEmpty()) {
            DialogUtil.showWarning(this, "请输入身份证号");
            idNumberField.requestFocus();
            return;
        }

        // 简单的身份证号格式验证
        if (!(idNumber.length() == 18)) {
            DialogUtil.showWarning(this, "请输入正确的身份证号格式（十八位）");
            idNumberField.requestFocus();
            return;
        }

        // 创建业主数据服务对象
        OwnerDataService ownerDataService = new OwnerDataService(
                name, sex, houseNumber, phone, idNumber,
                remark, emergencyContact, emergencyPhone
        );

        // 插入业主信息
        boolean success = ownerDataService.insertOwnerInfo();

        if (success) {
            DialogUtil.showSuccess(this, "业主信息添加成功！");
            // 清空输入框
            nameField.setText("");
            sexComboBox.setSelectedIndex(0);
            houseNumberField.setText("");
            phoneField.setText("");
            idNumberField.setText("");
            remarkField.setText("");
            emergencyContactField.setText("");
            emergencyPhoneField.setText("");

            // 如果有业主信息视图引用，刷新列表
            if (ownerInfoView != null) {
                ownerInfoView.queryOwnerInfo();
            }
        } else {
            DialogUtil.showError(this, "业主信息添加失败，请重试！");
        }
    }
}
