package com.property.view;

import java.awt.EventQueue;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.border.EmptyBorder;

import com.property.util.DialogUtil;
import com.property.util.PasswordService;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.RoundedButton;

/**
 * 修改密码界面
 */
public class ChangePasswordView extends JFrame {

    private static final long serialVersionUID = 1L;
    private JPanel contentPane;
    private JPasswordField passwordField;

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    ChangePasswordView frame = new ChangePasswordView();
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建修改密码界面
     */
    public ChangePasswordView() {
        setTitle("修改密码");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 455, 300);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        contentPane = new JPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));

        setContentPane(contentPane);
        contentPane.setLayout(null);

        // 标题标签
        JLabel titleLabel = new JLabel("修改密码");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 22));
        titleLabel.setForeground(UIStyleUtil.PRIMARY_COLOR);
        titleLabel.setBounds(180, 20, 100, 40);
        contentPane.add(titleLabel);

        JLabel passwordLabel = new JLabel("输入新密码：");
        passwordLabel.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        passwordLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        passwordLabel.setBounds(41, 81, 162, 40);
        contentPane.add(passwordLabel);

        passwordField = new JPasswordField();
        passwordField.setFont(new Font("微软雅黑", Font.PLAIN, 20));
        passwordField.setBounds(181, 81, 213, 40);
        contentPane.add(passwordField);

        RoundedButton confirmButton = new RoundedButton("确认", UIStyleUtil.PRIMARY_COLOR, UIStyleUtil.SECONDARY_COLOR);
        confirmButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                // 获取密码
                String newPassword = new String(passwordField.getPassword());

                // 验证密码不能为空
                if (newPassword.trim().isEmpty()) {
                    DialogUtil.showWarning(ChangePasswordView.this, "密码不能为空！");
                    return;
                }

                // 获取当前登录用户名
                String currentUserName = LoginView.currentUserName;

                // 验证用户名不能为空
                if (currentUserName == null || currentUserName.trim().isEmpty()) {
                    DialogUtil.showWarning(ChangePasswordView.this, "您尚未登录，无法修改密码！");
                    return;
                }

                // 创建密码服务对象
                PasswordService passwordService = new PasswordService(newPassword, currentUserName);

                // 更新密码
                boolean success = passwordService.updatePassword();

                if (success) {
                    DialogUtil.showSuccess(ChangePasswordView.this, "密码修改成功！");
                    passwordField.setText(""); // 清空密码框
                } else {
                    DialogUtil.showError(ChangePasswordView.this, "密码修改失败，请重试！");
                }
            }
        });
        confirmButton.setBounds(63, 176, 134, 40);
        contentPane.add(confirmButton);

        RoundedButton clearButton = new RoundedButton("清空", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        clearButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                passwordField.setText("");
            }
        });
        clearButton.setBounds(236, 176, 134, 40);
        contentPane.add(clearButton);

    }
}
