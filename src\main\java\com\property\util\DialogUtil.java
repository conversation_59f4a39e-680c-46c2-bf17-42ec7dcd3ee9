package com.property.util;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Frame;
import java.awt.Window;

import javax.swing.BorderFactory;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.UIManager;
import javax.swing.border.Border;
import javax.swing.plaf.ColorUIResource;

/**
 * 对话框工具类 - 提供统一的现代化对话框风格
 */
public class DialogUtil {
    // 对话框类型
    public static final int SUCCESS_MESSAGE = JOptionPane.INFORMATION_MESSAGE;
    public static final int ERROR_MESSAGE = JOptionPane.ERROR_MESSAGE;
    public static final int WARNING_MESSAGE = JOptionPane.WARNING_MESSAGE;
    public static final int INFO_MESSAGE = JOptionPane.PLAIN_MESSAGE;
    public static final int QUESTION_MESSAGE = JOptionPane.QUESTION_MESSAGE;

    // 对话框按钮类型
    public static final int OK_OPTION = JOptionPane.OK_OPTION;
    public static final int YES_NO_OPTION = JOptionPane.YES_NO_OPTION;
    public static final int YES_NO_CANCEL_OPTION = JOptionPane.YES_NO_CANCEL_OPTION;
    public static final int OK_CANCEL_OPTION = JOptionPane.OK_CANCEL_OPTION;

    // 对话框返回值
    public static final int YES_OPTION = JOptionPane.YES_OPTION;
    public static final int NO_OPTION = JOptionPane.NO_OPTION;
    public static final int CANCEL_OPTION = JOptionPane.CANCEL_OPTION;
    public static final int CLOSED_OPTION = JOptionPane.CLOSED_OPTION;

    // 图标缓存
    private static ImageIcon successIcon;
    private static ImageIcon errorIcon;
    private static ImageIcon warningIcon;
    private static ImageIcon infoIcon;
    private static ImageIcon questionIcon;

    /**
     * 应用现代化样式到对话框
     */
    private static void applyModernStyle() {
        // 设置对话框背景色
        UIManager.put("OptionPane.background", new ColorUIResource(Color.WHITE));
        UIManager.put("Panel.background", new ColorUIResource(Color.WHITE));

        // 设置对话框标题和消息字体
        UIManager.put("OptionPane.messageFont", new Font("微软雅黑", Font.PLAIN, 14));
        UIManager.put("OptionPane.buttonFont", new Font("微软雅黑", Font.BOLD, 14));
        UIManager.put("OptionPane.titleFont", new Font("微软雅黑", Font.BOLD, 16));

        // 设置按钮样式
        UIManager.put("Button.background", UIStyleUtil.PRIMARY_COLOR);
        UIManager.put("Button.foreground", Color.WHITE);
        UIManager.put("Button.focus", new ColorUIResource(new Color(0, 0, 0, 0)));

        // 设置边框
        Border emptyBorder = BorderFactory.createEmptyBorder(10, 15, 10, 15);
        UIManager.put("OptionPane.border", emptyBorder);
        UIManager.put("Button.border", BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(UIStyleUtil.PRIMARY_COLOR, 1, true),
                BorderFactory.createEmptyBorder(5, 15, 5, 15)));

        // 调整图标和文字的布局
        UIManager.put("OptionPane.messageAreaBorder", BorderFactory.createEmptyBorder(0, 0, 0, 0));
        UIManager.put("OptionPane.iconPadding", 10); // 图标和文字之间的间距
        UIManager.put("OptionPane.messageForeground", UIStyleUtil.TEXT_COLOR); // 消息文字颜色

        // 设置图标位置为左侧
        UIManager.put("OptionPane.isYesLast", true); // 确保“是”按钮在右边
        UIManager.put("OptionPane.layoutDirection", javax.swing.SwingConstants.HORIZONTAL); // 水平布局
    }

    /**
     * 获取自定义图标
     */
    private static Icon getCustomIcon(int messageType) {
        // 初始化图标（如果需要）
        initIcons();

        switch (messageType) {
            case SUCCESS_MESSAGE:
                return successIcon;
            case ERROR_MESSAGE:
                return errorIcon;
            case WARNING_MESSAGE:
                return warningIcon;
            case QUESTION_MESSAGE:
                return questionIcon;
            case INFO_MESSAGE:
            default:
                return infoIcon;
        }
    }

    /**
     * 初始化图标
     */
    private static void initIcons() {
        if (successIcon == null) {
            successIcon = IconGenerator.createSuccessIcon();
        }
        if (errorIcon == null) {
            errorIcon = IconGenerator.createErrorIcon();
        }
        if (warningIcon == null) {
            warningIcon = IconGenerator.createWarningIcon();
        }
        if (infoIcon == null) {
            infoIcon = IconGenerator.createInfoIcon();
        }
        if (questionIcon == null) {
            questionIcon = IconGenerator.createQuestionIcon();
        }
    }

    /**
     * 显示消息对话框
     *
     * @param parentComponent 父组件
     * @param message 消息内容
     * @param title 标题
     * @param messageType 消息类型
     */
    public static void showMessageDialog(Component parentComponent, Object message, String title, int messageType) {
        applyModernStyle();

        // 创建水平布局的消息面板
        JPanel messagePanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT, 10, 10));
        messagePanel.setBackground(Color.WHITE);

        // 添加图标
        JLabel iconLabel = new JLabel(getCustomIcon(messageType));
        messagePanel.add(iconLabel);

        // 添加消息文本
        JLabel messageLabel = new JLabel(message.toString());
        messageLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        messagePanel.add(messageLabel);

        // 创建自定义对话框 - 不显示默认图标
        JOptionPane optionPane = new JOptionPane(
                messagePanel,
                JOptionPane.PLAIN_MESSAGE, // 使用PLAIN_MESSAGE不显示默认图标
                JOptionPane.DEFAULT_OPTION);

        JDialog dialog = optionPane.createDialog(parentComponent, title);
        dialog.setMinimumSize(new Dimension(300, 150));
        centerDialog(dialog, parentComponent);
        dialog.setVisible(true);
    }

    /**
     * 显示确认对话框
     *
     * @param parentComponent 父组件
     * @param message 消息内容
     * @param title 标题
     * @param optionType 选项类型
     * @param messageType 消息类型
     * @return 用户选择的选项
     */
    public static int showConfirmDialog(Component parentComponent, Object message, String title, int optionType, int messageType) {
        applyModernStyle();

        // 创建水平布局的消息面板
        JPanel messagePanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT, 10, 10));
        messagePanel.setBackground(Color.WHITE);

        // 添加图标
        JLabel iconLabel = new JLabel(getCustomIcon(messageType));
        messagePanel.add(iconLabel);

        // 添加消息文本
        JLabel messageLabel = new JLabel(message.toString());
        messageLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        messagePanel.add(messageLabel);

        // 创建自定义对话框 - 不显示默认图标
        JOptionPane optionPane = new JOptionPane(
                messagePanel,
                JOptionPane.PLAIN_MESSAGE, // 使用PLAIN_MESSAGE不显示默认图标
                optionType);

        JDialog dialog = optionPane.createDialog(parentComponent, title);
        dialog.setMinimumSize(new Dimension(300, 150));
        centerDialog(dialog, parentComponent);
        dialog.setVisible(true);

        Object selectedValue = optionPane.getValue();
        if (selectedValue == null) {
            return CLOSED_OPTION;
        }

        if (selectedValue instanceof Integer) {
            return ((Integer) selectedValue).intValue();
        }

        return CLOSED_OPTION;
    }

    /**
     * 显示成功消息
     */
    public static void showSuccess(Component parentComponent, String message) {
        showMessageDialog(parentComponent, message, "成功", SUCCESS_MESSAGE);
    }

    /**
     * 显示错误消息
     */
    public static void showError(Component parentComponent, String message) {
        showMessageDialog(parentComponent, message, "错误", ERROR_MESSAGE);
    }

    /**
     * 显示警告消息
     */
    public static void showWarning(Component parentComponent, String message) {
        showMessageDialog(parentComponent, message, "警告", WARNING_MESSAGE);
    }

    /**
     * 显示信息消息
     */
    public static void showInfo(Component parentComponent, String message) {
        showMessageDialog(parentComponent, message, "提示", INFO_MESSAGE);
    }

    /**
     * 显示确认对话框
     */
    public static boolean showConfirm(Component parentComponent, String message) {
        int result = showConfirmDialog(parentComponent, message, "确认", YES_NO_OPTION, QUESTION_MESSAGE);
        return result == YES_OPTION;
    }

    /**
     * 显示信息对话框（简化版）
     * @param parent 父组件
     * @param message 消息内容
     */
    public static void showInfoDialog(Component parent, String message) {
        showInfo(parent, message);
    }

    /**
     * 显示警告对话框（简化版）
     * @param parent 父组件
     * @param message 消息内容
     */
    public static void showWarningDialog(Component parent, String message) {
        showWarning(parent, message);
    }

    /**
     * 显示错误对话框（简化版）
     * @param parent 父组件
     * @param message 消息内容
     */
    public static void showErrorDialog(Component parent, String message) {
        showError(parent, message);
    }

    /**
     * 居中显示对话框
     */
    private static void centerDialog(JDialog dialog, Component parent) {
        if (parent == null) {
            dialog.setLocationRelativeTo(null);
            return;
        }

        Window window = null;
        if (parent instanceof Window) {
            window = (Window) parent;
        } else {
            window = javax.swing.SwingUtilities.getWindowAncestor(parent);
        }

        if (window instanceof Frame) {
            dialog.setLocationRelativeTo(window);
        } else {
            dialog.setLocationRelativeTo(null);
        }
    }
}
