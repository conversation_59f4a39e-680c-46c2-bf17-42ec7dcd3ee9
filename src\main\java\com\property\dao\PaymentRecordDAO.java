package com.property.dao;

import com.property.model.PaymentRecord;
import com.property.util.DatabaseConfig;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 缴费记录数据访问对象
 * 负责与t_payment_record表的交互
 */
public class PaymentRecordDAO {
    
    /**
     * 获取所有缴费记录
     * @return 缴费记录列表
     */
    public List<PaymentRecord> getAllRecords() {
        List<PaymentRecord> records = new ArrayList<>();
        String sql = "SELECT r.*, o.name AS owner_name, b.bill_no " +
                     "FROM t_payment_record r " +
                     "JOIN t_owner_info o ON r.owner_id = o.id " +
                     "JOIN t_payment_bill b ON r.bill_id = b.id " +
                     "ORDER BY r.payment_time DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                PaymentRecord record = extractRecordFromResultSet(rs);
                records.add(record);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * 根据业主ID获取缴费记录
     * @param ownerId 业主ID
     * @return 缴费记录列表
     */
    public List<PaymentRecord> getRecordsByOwnerId(int ownerId) {
        List<PaymentRecord> records = new ArrayList<>();
        String sql = "SELECT r.*, o.name AS owner_name, b.bill_no " +
                     "FROM t_payment_record r " +
                     "JOIN t_owner_info o ON r.owner_id = o.id " +
                     "JOIN t_payment_bill b ON r.bill_id = b.id " +
                     "WHERE r.owner_id = ? " +
                     "ORDER BY r.payment_time DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, ownerId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    PaymentRecord record = extractRecordFromResultSet(rs);
                    records.add(record);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * 根据账单ID获取缴费记录
     * @param billId 账单ID
     * @return 缴费记录列表
     */
    public List<PaymentRecord> getRecordsByBillId(int billId) {
        List<PaymentRecord> records = new ArrayList<>();
        String sql = "SELECT r.*, o.name AS owner_name, b.bill_no " +
                     "FROM t_payment_record r " +
                     "JOIN t_owner_info o ON r.owner_id = o.id " +
                     "JOIN t_payment_bill b ON r.bill_id = b.id " +
                     "WHERE r.bill_id = ? " +
                     "ORDER BY r.payment_time DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, billId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    PaymentRecord record = extractRecordFromResultSet(rs);
                    records.add(record);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * 根据ID获取缴费记录
     * @param id 缴费记录ID
     * @return 缴费记录对象，如果不存在则返回null
     */
    public PaymentRecord getRecordById(int id) {
        String sql = "SELECT r.*, o.name AS owner_name, b.bill_no " +
                     "FROM t_payment_record r " +
                     "JOIN t_owner_info o ON r.owner_id = o.id " +
                     "JOIN t_payment_bill b ON r.bill_id = b.id " +
                     "WHERE r.id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return extractRecordFromResultSet(rs);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 添加缴费记录
     * @param record 缴费记录对象
     * @return 添加成功返回生成的ID，失败返回-1
     */
    public int addRecord(PaymentRecord record) {
        String sql = "INSERT INTO t_payment_record (record_no, bill_id, owner_id, payment_amount, " +
                     "payment_method, payment_time, operator, receipt_no, remark) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, record.getRecordNo());
            pstmt.setInt(2, record.getBillId());
            pstmt.setInt(3, record.getOwnerId());
            pstmt.setBigDecimal(4, record.getPaymentAmount());
            pstmt.setString(5, record.getPaymentMethod());
            pstmt.setTimestamp(6, record.getPaymentTime());
            pstmt.setString(7, record.getOperator());
            pstmt.setString(8, record.getReceiptNo());
            pstmt.setString(9, record.getRemark());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        return generatedKeys.getInt(1);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return -1;
    }
    
    /**
     * 删除缴费记录
     * @param id 缴费记录ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteRecord(int id) {
        String sql = "DELETE FROM t_payment_record WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成缴费记录编号
     * @return 缴费记录编号
     */
    public String generateRecordNo() {
        return "PR" + System.currentTimeMillis();
    }
    
    /**
     * 生成收据编号
     * @return 收据编号
     */
    public String generateReceiptNo() {
        return "RC" + System.currentTimeMillis();
    }
    
    /**
     * 从ResultSet中提取缴费记录对象
     * @param rs ResultSet
     * @return 缴费记录对象
     * @throws SQLException SQL异常
     */
    private PaymentRecord extractRecordFromResultSet(ResultSet rs) throws SQLException {
        PaymentRecord record = new PaymentRecord();
        record.setId(rs.getInt("id"));
        record.setRecordNo(rs.getString("record_no"));
        record.setBillId(rs.getInt("bill_id"));
        record.setOwnerId(rs.getInt("owner_id"));
        record.setPaymentAmount(rs.getBigDecimal("payment_amount"));
        record.setPaymentMethod(rs.getString("payment_method"));
        record.setPaymentTime(rs.getTimestamp("payment_time"));
        record.setOperator(rs.getString("operator"));
        record.setReceiptNo(rs.getString("receipt_no"));
        record.setRemark(rs.getString("remark"));
        record.setCreateTime(rs.getTimestamp("create_time"));
        record.setUpdateTime(rs.getTimestamp("update_time"));
        record.setOwnerName(rs.getString("owner_name"));
        record.setBillNo(rs.getString("bill_no"));
        return record;
    }
}
