package com.property.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.property.model.Owner;

/**
 * 业主数据访问对象，处理业主相关的数据库操作
 */
public class OwnerDao {

    /**
     * 插入业主信息
     * @param connection 数据库连接
     * @param owner 业主对象
     * @throws SQLException SQL异常
     */
    public void insert(Connection connection, Owner owner) throws SQLException {
        String sql = "INSERT INTO t_owner_info (name, gender, house_number, phone, id_number, remark, emergency_contact, emergency_phone) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, owner.getName());
        stmt.setString(2, owner.getSex());
        stmt.setString(3, owner.getHouseNumber());
        stmt.setString(4, owner.getPhone());
        stmt.setString(5, owner.getIdNumber());
        stmt.setString(6, owner.getRemark());
        stmt.setString(7, owner.getEmergencyContact());
        stmt.setString(8, owner.getEmergencyPhone());
        int rowsInserted = stmt.executeUpdate();
        if (rowsInserted > 0) {
            System.out.println("业主信息添加成功！");
        } else {
            System.out.println("业主信息添加失败！");
        }
    }

    /**
     * 插入报修请求
     * @param connection 数据库连接
     * @param owner 业主对象
     * @throws SQLException SQL异常
     */
    public void insertRepairRequest(Connection connection, Owner owner) throws SQLException {
        String sql = "INSERT INTO t_repair_request (name, house_number, repair_content) VALUES (?, ?, ?)";
        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, owner.getName());
        stmt.setString(2, owner.getHouseNumber());
        stmt.setString(3, owner.getRepairContent());
        int rowsInserted = stmt.executeUpdate();
        if (rowsInserted > 0) {
            System.out.println("报修请求添加成功！");
        } else {
            System.out.println("报修请求添加失败！");
        }
    }

    /**
     * 删除业主信息
     * @param connection 数据库连接
     * @param owner 业主对象
     * @return 是否删除成功
     * @throws SQLException SQL异常
     */
    public boolean delete(Connection connection, Owner owner) throws SQLException {
        boolean success = false;

        try {
            // 开始事务
            connection.setAutoCommit(false);

            // 先获取业主姓名，用于删除用户账户和报修请求
            String ownerName = "";
            if (owner.getId() > 0) {
                String sqlGetOwnerName = "SELECT name FROM t_owner_info WHERE id = ?";
                PreparedStatement stmtGetOwnerName = connection.prepareStatement(sqlGetOwnerName);
                stmtGetOwnerName.setInt(1, owner.getId());
                ResultSet rs = stmtGetOwnerName.executeQuery();
                if (rs.next()) {
                    ownerName = rs.getString("name");
                }
                rs.close();
                stmtGetOwnerName.close();
            } else {
                ownerName = owner.getName();
            }

            // 1. 删除用户账户表中的相关记录
            String sqlDeleteUserAccount = "DELETE FROM t_user_account WHERE user_name = ?";
            PreparedStatement stmtDeleteUserAccount = connection.prepareStatement(sqlDeleteUserAccount);
            stmtDeleteUserAccount.setString(1, ownerName);
            int userAccountRowsDeleted = stmtDeleteUserAccount.executeUpdate();
            System.out.println("删除用户账户记录: " + userAccountRowsDeleted + " 行");

            // 2. 删除报修请求表中的相关记录
            String sqlDeleteRepairRequest = "DELETE FROM t_repair_request WHERE name = ?";
            PreparedStatement stmtDeleteRepairRequest = connection.prepareStatement(sqlDeleteRepairRequest);
            stmtDeleteRepairRequest.setString(1, ownerName);
            int repairRequestRowsDeleted = stmtDeleteRepairRequest.executeUpdate();
            System.out.println("删除报修请求记录: " + repairRequestRowsDeleted + " 行");

            // 3. 删除业主信息表中的记录
            String sqlDeleteOwnerInfo;
            PreparedStatement stmtDeleteOwnerInfo;

            if (owner.getId() > 0) {
                // 如果有ID，优先使用ID删除
                sqlDeleteOwnerInfo = "DELETE FROM t_owner_info WHERE id = ?";
                stmtDeleteOwnerInfo = connection.prepareStatement(sqlDeleteOwnerInfo);
                stmtDeleteOwnerInfo.setInt(1, owner.getId());
            } else {
                // 否则使用姓名删除
                sqlDeleteOwnerInfo = "DELETE FROM t_owner_info WHERE name = ?";
                stmtDeleteOwnerInfo = connection.prepareStatement(sqlDeleteOwnerInfo);
                stmtDeleteOwnerInfo.setString(1, owner.getName());
            }

            int ownerInfoRowsDeleted = stmtDeleteOwnerInfo.executeUpdate();

            // 提交事务
            connection.commit();

            if (ownerInfoRowsDeleted > 0) {
                System.out.println("业主 " + owner.getName() + " 信息删除成功！");
                System.out.println("同时删除了 " + userAccountRowsDeleted + " 条用户账户记录和 " + repairRequestRowsDeleted + " 条报修请求记录");
                success = true;
            } else {
                System.out.println("业主信息删除失败！");
                success = false;
            }
        } catch (SQLException e) {
            // 发生异常时回滚事务
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            throw e;
        } finally {
            // 恢复自动提交
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

        return success;
    }

    /**
     * 删除报修请求
     * @param connection 数据库连接
     * @param owner 业主对象
     * @throws SQLException SQL异常
     */
    public void deleteRepairRequest(Connection connection, Owner owner) throws SQLException {
        String sql = "DELETE FROM t_repair_request WHERE name = ?";
        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, owner.getName());
        int rowsDeleted = stmt.executeUpdate();
        if (rowsDeleted > 0) {
            System.out.println("业主 " + owner.getName() + " 的报修请求删除成功！");
        } else {
            System.out.println("报修请求删除失败！");
        }
    }

    /**
     * 根据姓名查询业主信息
     * @param connection 数据库连接
     * @param owner 业主对象
     * @return 查询结果集
     * @throws SQLException SQL异常
     */
    public ResultSet queryOwnerByName(Connection connection, Owner owner) throws SQLException {
        ResultSet resultSet = null;
        String sql;
        PreparedStatement stmt;

        // 如果姓名为空，查询所有业主信息
        if (owner.getName() == null || owner.getName().trim().isEmpty()) {
            sql = "SELECT id, name, gender, house_number, phone, id_number, " +
                  "IFNULL(remark, '') as remark, " +
                  "IFNULL(emergency_contact, '') as emergency_contact, " +
                  "IFNULL(emergency_phone, '') as emergency_phone " +
                  "FROM t_owner_info ORDER BY name";
            stmt = connection.prepareStatement(sql);
        } else {
            // 如果有姓名，根据姓名查询
            sql = "SELECT id, name, gender, house_number, phone, id_number, " +
                  "IFNULL(remark, '') as remark, " +
                  "IFNULL(emergency_contact, '') as emergency_contact, " +
                  "IFNULL(emergency_phone, '') as emergency_phone " +
                  "FROM t_owner_info WHERE name LIKE ? ORDER BY name";
            stmt = connection.prepareStatement(sql);
            stmt.setString(1, "%" + owner.getName() + "%");
        }

        resultSet = stmt.executeQuery();
        return resultSet;
    }

    /**
     * 根据姓名查询报修请求
     * @param connection 数据库连接
     * @param owner 业主对象
     * @return 查询结果集
     * @throws SQLException SQL异常
     */
    public ResultSet queryRepairRequestByName(Connection connection, Owner owner) throws SQLException {
        ResultSet resultSet = null;
        String sql;
        PreparedStatement stmt;

        // 如果姓名为空，查询所有报修请求
        if (owner.getName() == null || owner.getName().trim().isEmpty()) {
            sql = "SELECT id, name, house_number, repair_content, status, create_time, update_time "
                + "FROM t_repair_request ORDER BY create_time DESC";
            stmt = connection.prepareStatement(sql);
        } else {
            // 如果有姓名，根据姓名查询
            sql = "SELECT id, name, house_number, repair_content, status, create_time, update_time "
                + "FROM t_repair_request WHERE name = ? ORDER BY create_time DESC";
            stmt = connection.prepareStatement(sql);
            stmt.setString(1, owner.getName());
        }

        resultSet = stmt.executeQuery();
        return resultSet;
    }

    /**
     * 验证管理员登录
     * @param connection 数据库连接
     * @param owner 业主对象
     * @return 是否登录成功
     * @throws SQLException SQL异常
     */
    public boolean validateAdminLogin(Connection connection, Owner owner) throws SQLException {
        String sql = "SELECT * FROM t_admin_account WHERE user_name = ? AND password = ?";
        ResultSet resultSet = null;
        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, owner.getName());
        stmt.setString(2, owner.getPassword());
        resultSet = stmt.executeQuery();
        return resultSet.next();
    }

    /**
     * 验证用户登录
     * @param connection 数据库连接
     * @param owner 业主对象
     * @return 是否登录成功
     * @throws SQLException SQL异常
     */
    public boolean validateUserLogin(Connection connection, Owner owner) throws SQLException {
        String sql = "SELECT * FROM t_user_account WHERE user_name = ? AND password = ?";
        ResultSet resultSet = null;
        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, owner.getName());
        stmt.setString(2, owner.getPassword());
        resultSet = stmt.executeQuery();
        return resultSet.next();
    }

    // 为了兼容旧代码，保留以下方法
    public void insert2(Connection con, Owner user) throws SQLException {
        insertRepairRequest(con, user);
    }

    public boolean delete2(Connection con, Owner user) throws SQLException {
        deleteRepairRequest(con, user);
        return true; // 为了兼容旧代码，始终返回true
    }

    public ResultSet inquire(Connection con, Owner user) throws SQLException {
        return queryOwnerByName(con, user);
    }

    public ResultSet inquire2(Connection con, Owner user) throws SQLException {
        return queryRepairRequestByName(con, user);
    }

    public boolean login(Connection con, Owner user) throws SQLException {
        return validateAdminLogin(con, user);
    }

    public boolean login2(Connection con, Owner user) throws SQLException {
        return validateUserLogin(con, user);
    }
}
