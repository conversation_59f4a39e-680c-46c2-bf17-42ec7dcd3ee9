package com.property.view;

import java.awt.EventQueue;
import java.awt.Font;
import java.awt.Color;
import java.awt.GradientPaint;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;

import java.sql.Connection;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.DefaultComboBoxModel;
import javax.swing.ImageIcon;

import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuBar;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JTextField;
import javax.swing.SwingConstants;
import javax.swing.border.EmptyBorder;

import javax.swing.plaf.basic.BasicComboBoxUI;

import com.property.dao.OwnerDao;
import com.property.model.Owner;
import com.property.util.DatabaseConnection;
import com.property.util.DialogUtil;
import com.property.util.StringUtil;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.RoundedButton;
import com.property.util.UIStyleUtil.RoundedBorder;
import com.property.util.UIStyleUtil.ShadowPanel;

/**
 * 登录界面 - 现代化风格
 */
public class LoginView extends JFrame {
    private static final long serialVersionUID = 1L;
    public static String userType; // 用户类型：管理员或普通用户
    private GradientPanel contentPane;
    private JTextField userNameField;
    public static String currentUserName; // 当前登录用户名
    private JPasswordField passwordField;
    private JLabel userNameLabel;
    private JLabel passwordLabel;
    private JLabel accountTypeLabel;
    private JLabel titleLabel;
    private JComboBox<String> accountTypeComboBox;
    private static LoginView frame = new LoginView();

    // 使用UIStyleUtil中的颜色方案
    private static final Color ACCENT_COLOR = UIStyleUtil.ACCENT_COLOR;
    private static final Color BACKGROUND_COLOR_START = UIStyleUtil.BACKGROUND_COLOR_START;
    private static final Color BACKGROUND_COLOR_END = UIStyleUtil.BACKGROUND_COLOR_END;
    private static final Color TEXT_COLOR = UIStyleUtil.TEXT_COLOR;
    private static final Color LABEL_COLOR = UIStyleUtil.LABEL_COLOR;
    private static final Color FIELD_BACKGROUND = UIStyleUtil.FIELD_BACKGROUND;
    private static final Color SECONDARY_COLOR = UIStyleUtil.SECONDARY_COLOR;

    /**
     * 渐变背景面板
     */
    class GradientPanel extends JPanel {
        private static final long serialVersionUID = 1L;

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g;
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int w = getWidth();
            int h = getHeight();

            // 绘制渐变背景
            GradientPaint gp = new GradientPaint(0, 0, BACKGROUND_COLOR_START, 0, h, BACKGROUND_COLOR_END);
            g2d.setPaint(gp);
            g2d.fillRect(0, 0, w, h);
        }
    }







    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建登录界面 - 现代化风格
     */
    public LoginView() {
        setTitle("物业管理系统");
        setResizable(false);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 600, 500);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            // 使用准备好的图标文件
            ImageIcon icon = new ImageIcon(LoginView.class.getResource("/imagine/图标.png"));
            setIconImage(icon.getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        contentPane = new GradientPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);
        contentPane.setLayout(null);

        // 创建现代化菜单栏作为标题栏
        JMenuBar titleBar = new JMenuBar();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setBorder(BorderFactory.createEmptyBorder());
        titleBar.setBounds(0, 0, 600, 50);
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));
        contentPane.add(titleBar);

        // 添加系统标题
        titleLabel = new JLabel("物业管理系统");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 40));
        titleBar.add(titleLabel);

        // 添加弹性空间，将登录信息推到右边
        titleBar.add(Box.createHorizontalGlue());

        // 显示登录标识
        JLabel loginLabel = new JLabel("用户登录");
        loginLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        loginLabel.setForeground(Color.WHITE);
        loginLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 20));
        titleBar.add(loginLabel);

        // 登录表单面板 - 使用带阴影的面板
        ShadowPanel formPanelContainer = new ShadowPanel();
        formPanelContainer.setBounds(100, 120, 400, 320);
        formPanelContainer.setLayout(null);
        contentPane.add(formPanelContainer);

        JPanel formPanel = new JPanel();
        formPanel.setOpaque(false);
        formPanel.setBounds(5, 5, 390, 310);
        formPanel.setLayout(null);
        formPanelContainer.add(formPanel);

        // 账户类型标签 - 使用更清晰的标签颜色
        accountTypeLabel = new JLabel("账户类型");
        accountTypeLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        accountTypeLabel.setForeground(LABEL_COLOR);
        accountTypeLabel.setBounds(25, 20, 100, 30);
        formPanel.add(accountTypeLabel);

        // 账户类型下拉框 - 增强的自定义样式
        accountTypeComboBox = new JComboBox<>();
        accountTypeComboBox.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        accountTypeComboBox.setModel(new DefaultComboBoxModel<>(new String[] { "用户", "管理员" }));
        accountTypeComboBox.setBounds(135, 20, 220, 35);
        accountTypeComboBox.setBackground(FIELD_BACKGROUND);
        accountTypeComboBox.setForeground(TEXT_COLOR);
        accountTypeComboBox.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(SECONDARY_COLOR, 10),
                BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        accountTypeComboBox.setUI(new BasicComboBoxUI());
        formPanel.add(accountTypeComboBox);

        // 用户名标签 - 使用更清晰的标签颜色
        userNameLabel = new JLabel("用户名");
        userNameLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        userNameLabel.setForeground(LABEL_COLOR);
        userNameLabel.setBounds(25, 70, 100, 30);
        formPanel.add(userNameLabel);

        // 用户名输入框 - 增强的自定义样式
        userNameField = new JTextField();
        userNameField.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        userNameField.setBounds(135, 70, 220, 35);
        userNameField.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(SECONDARY_COLOR, 10),
                BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        userNameField.setBackground(FIELD_BACKGROUND);
        userNameField.setForeground(TEXT_COLOR);
        userNameField.setColumns(10);

        // 添加焦点监听器以实现输入框高亮效果
        userNameField.addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                userNameField.setBorder(BorderFactory.createCompoundBorder(
                        new RoundedBorder(ACCENT_COLOR, 10),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
                userNameField.setBackground(Color.WHITE);
            }

            @Override
            public void focusLost(FocusEvent e) {
                userNameField.setBorder(BorderFactory.createCompoundBorder(
                        new RoundedBorder(SECONDARY_COLOR, 10),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
                userNameField.setBackground(FIELD_BACKGROUND);
            }
        });
        formPanel.add(userNameField);

        // 密码标签 - 使用更清晰的标签颜色
        passwordLabel = new JLabel("密码");
        passwordLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        passwordLabel.setForeground(LABEL_COLOR);
        passwordLabel.setBounds(25, 120, 100, 30);
        formPanel.add(passwordLabel);

        // 密码输入框 - 增强的自定义样式
        passwordField = new JPasswordField();
        passwordField.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        passwordField.setBounds(135, 120, 220, 35);
        passwordField.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(SECONDARY_COLOR, 10),
                BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        passwordField.setBackground(FIELD_BACKGROUND);
        passwordField.setForeground(TEXT_COLOR);

        // 添加焦点监听器以实现输入框高亮效果
        passwordField.addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                passwordField.setBorder(BorderFactory.createCompoundBorder(
                        new RoundedBorder(ACCENT_COLOR, 10),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
                passwordField.setBackground(Color.WHITE);
            }

            @Override
            public void focusLost(FocusEvent e) {
                passwordField.setBorder(BorderFactory.createCompoundBorder(
                        new RoundedBorder(SECONDARY_COLOR, 10),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
                passwordField.setBackground(FIELD_BACKGROUND);
            }
        });
        formPanel.add(passwordField);

        // 登录按钮 - 使用深蓝色
        RoundedButton loginButton = new RoundedButton("登录", UIStyleUtil.LOGIN_BUTTON_COLOR, UIStyleUtil.SECONDARY_COLOR);
        loginButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                currentUserName = userNameField.getText();
                if (accountTypeComboBox.getSelectedItem().equals("管理员")) {
                    userType = "管理员";
                    validateAdminLogin(e);
                } else {
                    userType = "用户";
                    validateUserLogin(e);
                }
            }
        });
        loginButton.setBounds(135, 180, 100, 40);
        formPanel.add(loginButton);

        // 注册按钮 - 使用蓝色
        RoundedButton registerButton = new RoundedButton("注册", UIStyleUtil.REGISTER_BUTTON_COLOR, UIStyleUtil.PRIMARY_COLOR);
        registerButton.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                new RegisterView().setVisible(true);
            }
        });
        registerButton.setBounds(255, 180, 100, 40);
        formPanel.add(registerButton);

        // 版权信息 - 更精美的字体和颜色
        JLabel copyrightLabel = new JLabel("© 2025 物业管理系统 - 数据库课程期末实训 - [杨爽 杨璐萍 李思涵]");
        copyrightLabel.setHorizontalAlignment(SwingConstants.CENTER);
        copyrightLabel.setFont(UIStyleUtil.COPYRIGHT_FONT);
        copyrightLabel.setForeground(new Color(120, 120, 120));
        copyrightLabel.setBounds(0, 440, 600, 20);
        contentPane.add(copyrightLabel);
    }

    /**
     * 验证管理员登录
     */
    private void validateAdminLogin(ActionEvent e) {
        String username = this.userNameField.getText();
        OwnerDao ownerDao = new OwnerDao();
        DatabaseConnection dbConnection = new DatabaseConnection();
        String password = new String(this.passwordField.getPassword());
        Owner owner = new Owner(username, password);

        if (StringUtil.isEmpty(username)) {
            DialogUtil.showWarning(this, "用户名不能为空！");
            return;
        }
        if (StringUtil.isEmpty(password)) {
            DialogUtil.showWarning(this, "密码不能为空！");
            return;
        }

        Connection connection = null;
        try {
            connection = dbConnection.getConnection();
            boolean isValidAdmin = ownerDao.validateAdminLogin(connection, owner);
            if (isValidAdmin) {
                DialogUtil.showSuccess(this, "登录成功!");
                new MainView().setVisible(true);
                dispose(); // 关闭登录窗口
            } else {
                DialogUtil.showError(this, "用户名或者密码错误！");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }



    /**
     * 验证普通用户登录
     */
    private void validateUserLogin(ActionEvent e) {
        String username = this.userNameField.getText();
        OwnerDao ownerDao = new OwnerDao();
        DatabaseConnection dbConnection = new DatabaseConnection();
        String password = new String(this.passwordField.getPassword());
        Owner owner = new Owner(username, password);

        if (StringUtil.isEmpty(username)) {
            DialogUtil.showWarning(this, "用户名不能为空！");
            return;
        }
        if (StringUtil.isEmpty(password)) {
            DialogUtil.showWarning(this, "密码不能为空！");
            return;
        }

        Connection connection = null;
        try {
            connection = dbConnection.getConnection();
            boolean isValidUser = ownerDao.validateUserLogin(connection, owner);
            if (isValidUser) {
                DialogUtil.showSuccess(this, "登录成功!");
                new MainView().setVisible(true);
                dispose(); // 关闭登录窗口
            } else {
                DialogUtil.showError(this, "用户名或者密码错误！");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
