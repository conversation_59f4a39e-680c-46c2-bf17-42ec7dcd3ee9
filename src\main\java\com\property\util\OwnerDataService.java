package com.property.util;

import java.sql.*;

/**
 * 业主数据服务类，用于向数据库写入业主相关数据
 */
public class OwnerDataService {
    private static String userName;
    private static String sex;
    private static String houseNumber;
    private static String phone;
    private static String idNumber;
    private static String remark;
    private static String emergencyContact;
    private static String emergencyPhone;

    /**
     * 完整业主信息的构造函数
     * @param userName 用户名
     * @param sex 性别
     * @param houseNumber 门牌号
     * @param phone 电话号码
     * @param idNumber 身份证号
     * @param remark 备注信息
     * @param emergencyContact 紧急联系人
     * @param emergencyPhone 紧急联系人电话
     */
    public OwnerDataService(String userName, String sex, String houseNumber, String phone, String idNumber,
                           String remark, String emergencyContact, String emergencyPhone) {
        super();
        OwnerDataService.userName = userName;
        OwnerDataService.sex = sex;
        OwnerDataService.houseNumber = houseNumber;
        OwnerDataService.phone = phone;
        OwnerDataService.idNumber = idNumber;
        OwnerDataService.remark = remark;
        OwnerDataService.emergencyContact = emergencyContact;
        OwnerDataService.emergencyPhone = emergencyPhone;
    }

    /**
     * 基本业主信息的构造函数（兼容旧代码）
     * @param userName 用户名
     * @param sex 性别
     * @param houseNumber 门牌号
     * @param phone 电话号码
     * @param idNumber 身份证号
     */
    public OwnerDataService(String userName, String sex, String houseNumber, String phone, String idNumber) {
        this(userName, sex, houseNumber, phone, idNumber, "", "", "");
    }

    /**
     * 用于用户注册的构造函数
     * @param userName 用户名
     * @param password 密码
     */
    public OwnerDataService(String userName, String password) {
        super();
        OwnerDataService.userName = userName;
        OwnerDataService.sex = password; // 注意：这里使用sex字段存储密码，是为了兼容旧代码
    }

    /**
     * 插入业主信息到数据库
     * @return 是否成功插入数据
     */
    public boolean insertOwnerInfo() {
        try (Connection conn = DriverManager.getConnection(
                DatabaseConfig.getJdbcUrl(),
                DatabaseConfig.getUsername(),
                DatabaseConfig.getPassword())) {
            String sql = "INSERT INTO t_owner_info (name, gender, house_number, phone, id_number, remark, emergency_contact, emergency_phone) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, userName);
            stmt.setString(2, sex);
            stmt.setString(3, houseNumber);
            stmt.setString(4, phone);
            stmt.setString(5, idNumber);
            stmt.setString(6, remark);
            stmt.setString(7, emergencyContact);
            stmt.setString(8, emergencyPhone);
            int rowsInserted = stmt.executeUpdate();
            if (rowsInserted > 0) {
                System.out.println("业主信息添加成功！");
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 插入用户账户信息到数据库
     * @return 是否成功插入数据
     */
    public boolean insertUserAccount() {
        try (Connection conn = DriverManager.getConnection(
                DatabaseConfig.getJdbcUrl(),
                DatabaseConfig.getUsername(),
                DatabaseConfig.getPassword())) {
            // 首先检查用户名是否已存在
            String checkSql = "SELECT COUNT(*) FROM t_user_account WHERE user_name = ?";
            PreparedStatement checkStmt = conn.prepareStatement(checkSql);
            checkStmt.setString(1, userName);
            ResultSet rs = checkStmt.executeQuery();
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("用户名已存在！");
                DialogUtil.showError(null, "用户名已存在，请选择其他用户名");
                return false;
            }

            // 插入新用户
            String sql = "INSERT INTO t_user_account (user_name, password) VALUES (?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, userName);
            stmt.setString(2, sex); // 注意：这里使用sex字段存储密码，是为了兼容旧代码
            int rowsInserted = stmt.executeUpdate();
            if (rowsInserted > 0) {
                System.out.println("用户账户添加成功！");
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
            DialogUtil.showError(null, "注册失败: " + e.getMessage());
        }
        return false;
    }

    // 为了兼容旧代码，保留以下方法
    public boolean f() {
        return insertOwnerInfo();
    }

    public boolean f1() {
        return insertUserAccount();
    }
}
