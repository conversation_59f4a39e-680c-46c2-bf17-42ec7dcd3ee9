package com.property.view;

import com.property.model.FeeType;
import com.property.service.PaymentService;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.util.List;

/**
 * 费用类型管理界面
 */
public class FeeTypeManagementView extends JFrame {
    private PaymentService paymentService;
    private JTable feeTypeTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JButton searchButton;
    private JButton addButton;
    private JButton editButton;
    private JButton deleteButton;
    private JButton refreshButton;

    public FeeTypeManagementView() {
        paymentService = new PaymentService();
        initComponents();
        loadFeeTypes();
    }

    private void initComponents() {
        setTitle("费用类型管理");
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);

        // 创建标题栏
        JPanel titleBar = new JPanel();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setPreferredSize(new Dimension(800, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));

        // 添加系统标题
        JLabel titleLabel = new JLabel("费用类型管理");
        titleLabel.setFont(UIStyleUtil.MENU_FONT);
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 0));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建表格模型
        String[] columnNames = {"ID", "费用类型名称", "描述", "单价", "计费周期", "计费方式", "状态"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 设置表格不可编辑
            }
        };
        feeTypeTable = new JTable(tableModel);
        feeTypeTable.getTableHeader().setReorderingAllowed(false);
        feeTypeTable.getTableHeader().setResizingAllowed(true);
        feeTypeTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        feeTypeTable.setRowHeight(25);

        // 设置表格列宽
        feeTypeTable.getColumnModel().getColumn(0).setPreferredWidth(50);
        feeTypeTable.getColumnModel().getColumn(1).setPreferredWidth(150);
        feeTypeTable.getColumnModel().getColumn(2).setPreferredWidth(200);
        feeTypeTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        feeTypeTable.getColumnModel().getColumn(4).setPreferredWidth(100);
        feeTypeTable.getColumnModel().getColumn(5).setPreferredWidth(100);
        feeTypeTable.getColumnModel().getColumn(6).setPreferredWidth(80);

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(feeTypeTable);

        // 创建搜索面板
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setOpaque(false);
        searchField = new JTextField(20);
        UIStyleUtil.styleTextField(searchField);

        searchButton = new UIStyleUtil.RoundedButton("搜索", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        JLabel nameLabel = new JLabel("费用类型名称:");
        nameLabel.setFont(UIStyleUtil.LABEL_FONT);
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        searchPanel.add(nameLabel);
        searchPanel.add(searchField);
        searchPanel.add(searchButton);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        addButton = new UIStyleUtil.RoundedButton("添加", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        editButton = new UIStyleUtil.RoundedButton("编辑", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        deleteButton = new UIStyleUtil.RoundedButton("删除", new Color(231, 76, 60), UIStyleUtil.SECONDARY_COLOR);
        refreshButton = new UIStyleUtil.RoundedButton("刷新", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(refreshButton);

        // 创建顶部面板
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setOpaque(false);
        topPanel.add(searchPanel, BorderLayout.WEST);
        topPanel.add(buttonPanel, BorderLayout.EAST);

        // 创建内容面板
        JPanel mainPanel = new UIStyleUtil.ShadowPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 设置表格样式
        feeTypeTable.getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 14));
        feeTypeTable.getTableHeader().setBackground(UIStyleUtil.PRIMARY_COLOR);
        feeTypeTable.getTableHeader().setForeground(Color.WHITE);

        // 设置表格条纹效果
        feeTypeTable.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (isSelected) {
                    c.setBackground(new Color(232, 242, 254));
                    c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                } else {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 247, 250));
                    }
                    c.setForeground(UIStyleUtil.TEXT_COLOR);
                }

                ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                ((JLabel) c).setHorizontalAlignment(column == 0 ? JLabel.CENTER : JLabel.LEFT);

                return c;
            }
        });

        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // 创建北部面板，包含标题栏和顶部面板
        JPanel northPanel = new JPanel(new BorderLayout());
        northPanel.setOpaque(false);
        northPanel.add(titleBar, BorderLayout.NORTH);
        northPanel.add(topPanel, BorderLayout.SOUTH);

        // 设置布局
        setLayout(new BorderLayout());
        add(northPanel, BorderLayout.NORTH);
        add(mainPanel, BorderLayout.CENTER);

        // 添加事件监听器
        addButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                showAddFeeTypeDialog();
            }
        });

        editButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = feeTypeTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) feeTypeTable.getValueAt(selectedRow, 0);
                    showEditFeeTypeDialog(id);
                } else {
                    DialogUtil.showWarningDialog(FeeTypeManagementView.this, "请先选择一个费用类型");
                }
            }
        });

        deleteButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = feeTypeTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) feeTypeTable.getValueAt(selectedRow, 0);
                    deleteFeeType(id);
                } else {
                    DialogUtil.showWarningDialog(FeeTypeManagementView.this, "请先选择一个费用类型");
                }
            }
        });

        refreshButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                loadFeeTypes();
            }
        });

        searchButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                searchFeeTypes();
            }
        });
    }

    /**
     * 加载所有费用类型
     */
    private void loadFeeTypes() {
        // 清空表格
        tableModel.setRowCount(0);

        // 获取所有费用类型
        List<FeeType> feeTypes = paymentService.getAllFeeTypes();

        // 填充表格
        for (FeeType feeType : feeTypes) {
            Object[] rowData = {
                feeType.getId(),
                feeType.getName(),
                feeType.getDescription(),
                feeType.getUnitPrice(),
                feeType.getBillingCycle(),
                feeType.getCalculationMethod(),
                feeType.isActive() ? "启用" : "禁用"
            };
            tableModel.addRow(rowData);
        }
    }

    /**
     * 搜索费用类型
     */
    private void searchFeeTypes() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            loadFeeTypes();
            return;
        }

        // 清空表格
        tableModel.setRowCount(0);

        // 获取所有费用类型
        List<FeeType> feeTypes = paymentService.getAllFeeTypes();

        // 筛选并填充表格
        for (FeeType feeType : feeTypes) {
            if (feeType.getName().contains(searchText)) {
                Object[] rowData = {
                    feeType.getId(),
                    feeType.getName(),
                    feeType.getDescription(),
                    feeType.getUnitPrice(),
                    feeType.getBillingCycle(),
                    feeType.getCalculationMethod(),
                    feeType.isActive() ? "启用" : "禁用"
                };
                tableModel.addRow(rowData);
            }
        }
    }

    /**
     * 显示添加费用类型对话框
     */
    private void showAddFeeTypeDialog() {
        JDialog dialog = new JDialog(this, "添加费用类型", true);
        dialog.setSize(400, 350);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        JPanel formPanel = new JPanel(new GridLayout(6, 2, 10, 10));
        formPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JTextField nameField = new JTextField();
        JTextField descriptionField = new JTextField();
        JTextField unitPriceField = new JTextField();

        String[] billingCycles = {"月度", "季度", "年度"};
        JComboBox<String> billingCycleComboBox = new JComboBox<>(billingCycles);

        String[] calculationMethods = {"固定金额", "按面积", "按用量"};
        JComboBox<String> calculationMethodComboBox = new JComboBox<>(calculationMethods);

        JCheckBox activeCheckBox = new JCheckBox("启用");
        activeCheckBox.setSelected(true);

        formPanel.add(new JLabel("费用类型名称:"));
        formPanel.add(nameField);
        formPanel.add(new JLabel("描述:"));
        formPanel.add(descriptionField);
        formPanel.add(new JLabel("单价:"));
        formPanel.add(unitPriceField);
        formPanel.add(new JLabel("计费周期:"));
        formPanel.add(billingCycleComboBox);
        formPanel.add(new JLabel("计费方式:"));
        formPanel.add(calculationMethodComboBox);
        formPanel.add(new JLabel("状态:"));
        formPanel.add(activeCheckBox);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton saveButton = new JButton("保存");
        JButton cancelButton = new JButton("取消");
        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);

        dialog.add(formPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 验证输入
                String name = nameField.getText().trim();
                String description = descriptionField.getText().trim();
                String unitPriceText = unitPriceField.getText().trim();

                if (name.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入费用类型名称");
                    return;
                }

                if (unitPriceText.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入单价");
                    return;
                }

                BigDecimal unitPrice;
                try {
                    unitPrice = new BigDecimal(unitPriceText);
                    if (unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        DialogUtil.showWarningDialog(dialog, "单价必须大于0");
                        return;
                    }
                } catch (NumberFormatException ex) {
                    DialogUtil.showWarningDialog(dialog, "单价必须是有效的数字");
                    return;
                }

                // 创建费用类型对象
                FeeType feeType = new FeeType();
                feeType.setName(name);
                feeType.setDescription(description);
                feeType.setUnitPrice(unitPrice);
                feeType.setBillingCycle((String) billingCycleComboBox.getSelectedItem());
                feeType.setCalculationMethod((String) calculationMethodComboBox.getSelectedItem());
                feeType.setActive(activeCheckBox.isSelected());

                // 保存费用类型
                boolean success = paymentService.addFeeType(feeType);
                if (success) {
                    DialogUtil.showInfoDialog(dialog, "费用类型添加成功");
                    dialog.dispose();
                    loadFeeTypes();
                } else {
                    DialogUtil.showErrorDialog(dialog, "费用类型添加失败，可能是名称已存在");
                }
            }
        });

        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 显示编辑费用类型对话框
     * @param id 费用类型ID
     */
    private void showEditFeeTypeDialog(int id) {
        FeeType feeType = paymentService.getFeeTypeById(id);
        if (feeType == null) {
            DialogUtil.showErrorDialog(this, "获取费用类型信息失败");
            return;
        }

        JDialog dialog = new JDialog(this, "编辑费用类型", true);
        dialog.setSize(400, 350);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        JPanel formPanel = new JPanel(new GridLayout(6, 2, 10, 10));
        formPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JTextField nameField = new JTextField(feeType.getName());
        JTextField descriptionField = new JTextField(feeType.getDescription());
        JTextField unitPriceField = new JTextField(feeType.getUnitPrice().toString());

        String[] billingCycles = {"月度", "季度", "年度"};
        JComboBox<String> billingCycleComboBox = new JComboBox<>(billingCycles);
        billingCycleComboBox.setSelectedItem(feeType.getBillingCycle());

        String[] calculationMethods = {"固定金额", "按面积", "按用量"};
        JComboBox<String> calculationMethodComboBox = new JComboBox<>(calculationMethods);
        calculationMethodComboBox.setSelectedItem(feeType.getCalculationMethod());

        JCheckBox activeCheckBox = new JCheckBox("启用");
        activeCheckBox.setSelected(feeType.isActive());

        formPanel.add(new JLabel("费用类型名称:"));
        formPanel.add(nameField);
        formPanel.add(new JLabel("描述:"));
        formPanel.add(descriptionField);
        formPanel.add(new JLabel("单价:"));
        formPanel.add(unitPriceField);
        formPanel.add(new JLabel("计费周期:"));
        formPanel.add(billingCycleComboBox);
        formPanel.add(new JLabel("计费方式:"));
        formPanel.add(calculationMethodComboBox);
        formPanel.add(new JLabel("状态:"));
        formPanel.add(activeCheckBox);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton saveButton = new JButton("保存");
        JButton cancelButton = new JButton("取消");
        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);

        dialog.add(formPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 验证输入
                String name = nameField.getText().trim();
                String description = descriptionField.getText().trim();
                String unitPriceText = unitPriceField.getText().trim();

                if (name.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入费用类型名称");
                    return;
                }

                if (unitPriceText.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入单价");
                    return;
                }

                BigDecimal unitPrice;
                try {
                    unitPrice = new BigDecimal(unitPriceText);
                    if (unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        DialogUtil.showWarningDialog(dialog, "单价必须大于0");
                        return;
                    }
                } catch (NumberFormatException ex) {
                    DialogUtil.showWarningDialog(dialog, "单价必须是有效的数字");
                    return;
                }

                // 更新费用类型对象
                feeType.setName(name);
                feeType.setDescription(description);
                feeType.setUnitPrice(unitPrice);
                feeType.setBillingCycle((String) billingCycleComboBox.getSelectedItem());
                feeType.setCalculationMethod((String) calculationMethodComboBox.getSelectedItem());
                feeType.setActive(activeCheckBox.isSelected());

                // 保存费用类型
                boolean success = paymentService.updateFeeType(feeType);
                if (success) {
                    DialogUtil.showInfoDialog(dialog, "费用类型更新成功");
                    dialog.dispose();
                    loadFeeTypes();
                } else {
                    DialogUtil.showErrorDialog(dialog, "费用类型更新失败，可能是名称已存在");
                }
            }
        });

        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 删除费用类型
     * @param id 费用类型ID
     */
    private void deleteFeeType(int id) {
        int option = JOptionPane.showConfirmDialog(this,
                "确定要删除该费用类型吗？删除后无法恢复。",
                "确认删除",
                JOptionPane.YES_NO_OPTION);

        if (option == JOptionPane.YES_OPTION) {
            boolean success = paymentService.deleteFeeType(id);
            if (success) {
                DialogUtil.showInfoDialog(this, "费用类型删除成功");
                loadFeeTypes();
            } else {
                DialogUtil.showErrorDialog(this, "费用类型删除失败，可能已被使用");
            }
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                new FeeTypeManagementView().setVisible(true);
            }
        });
    }
}
