package com.property.util;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.geom.Ellipse2D;
import java.awt.geom.GeneralPath;
import java.awt.image.BufferedImage;

import javax.swing.ImageIcon;

/**
 * 图标生成器 - 用于生成各种对话框图标
 */
public class IconGenerator {
    // 图标尺寸
    private static final int ICON_SIZE = 32;
    
    // 颜色定义
    private static final Color SUCCESS_COLOR = new Color(46, 204, 113); // 绿色
    private static final Color ERROR_COLOR = new Color(231, 76, 60);    // 红色
    private static final Color WARNING_COLOR = new Color(241, 196, 15); // 黄色
    private static final Color INFO_COLOR = new Color(52, 152, 219);    // 蓝色
    private static final Color QUESTION_COLOR = new Color(155, 89, 182); // 紫色
    
    /**
     * 生成成功图标
     */
    public static ImageIcon createSuccessIcon() {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = image.createGraphics();
        setupGraphics(g2);
        
        // 绘制圆形背景
        g2.setColor(SUCCESS_COLOR);
        g2.fill(new Ellipse2D.Float(0, 0, ICON_SIZE, ICON_SIZE));
        
        // 绘制对勾
        g2.setColor(Color.WHITE);
        GeneralPath path = new GeneralPath();
        path.moveTo(8, 16);
        path.lineTo(14, 22);
        path.lineTo(24, 10);
        g2.setStroke(new java.awt.BasicStroke(3));
        g2.draw(path);
        
        g2.dispose();
        return new ImageIcon(image);
    }
    
    /**
     * 生成错误图标
     */
    public static ImageIcon createErrorIcon() {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = image.createGraphics();
        setupGraphics(g2);
        
        // 绘制圆形背景
        g2.setColor(ERROR_COLOR);
        g2.fill(new Ellipse2D.Float(0, 0, ICON_SIZE, ICON_SIZE));
        
        // 绘制X
        g2.setColor(Color.WHITE);
        g2.setStroke(new java.awt.BasicStroke(3));
        g2.drawLine(10, 10, 22, 22);
        g2.drawLine(22, 10, 10, 22);
        
        g2.dispose();
        return new ImageIcon(image);
    }
    
    /**
     * 生成警告图标
     */
    public static ImageIcon createWarningIcon() {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = image.createGraphics();
        setupGraphics(g2);
        
        // 绘制三角形背景
        g2.setColor(WARNING_COLOR);
        GeneralPath triangle = new GeneralPath();
        triangle.moveTo(16, 2);
        triangle.lineTo(30, 28);
        triangle.lineTo(2, 28);
        triangle.closePath();
        g2.fill(triangle);
        
        // 绘制感叹号
        g2.setColor(Color.WHITE);
        g2.fillRect(15, 10, 3, 10);
        g2.fillRect(15, 22, 3, 3);
        
        g2.dispose();
        return new ImageIcon(image);
    }
    
    /**
     * 生成信息图标
     */
    public static ImageIcon createInfoIcon() {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = image.createGraphics();
        setupGraphics(g2);
        
        // 绘制圆形背景
        g2.setColor(INFO_COLOR);
        g2.fill(new Ellipse2D.Float(0, 0, ICON_SIZE, ICON_SIZE));
        
        // 绘制i
        g2.setColor(Color.WHITE);
        g2.fillRect(15, 8, 3, 3);
        g2.fillRect(15, 13, 3, 12);
        
        g2.dispose();
        return new ImageIcon(image);
    }
    
    /**
     * 生成问题图标
     */
    public static ImageIcon createQuestionIcon() {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = image.createGraphics();
        setupGraphics(g2);
        
        // 绘制圆形背景
        g2.setColor(QUESTION_COLOR);
        g2.fill(new Ellipse2D.Float(0, 0, ICON_SIZE, ICON_SIZE));
        
        // 绘制问号
        g2.setColor(Color.WHITE);
        g2.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 20));
        g2.drawString("?", 12, 23);
        
        g2.dispose();
        return new ImageIcon(image);
    }
    
    /**
     * 设置图形属性
     */
    private static void setupGraphics(Graphics2D g2) {
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
    }
    
    /**
     * 保存所有图标到指定目录
     */
    public static void saveAllIcons(String directory) {
        // 此方法用于将生成的图标保存到文件系统
        // 在实际应用中可以实现，这里暂不实现
    }
}
