package com.property.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 缴费记录模型类
 * 对应数据库中的t_payment_record表
 */
public class PaymentRecord {
    private int id;                 // 主键ID
    private String recordNo;        // 缴费记录编号
    private int billId;             // 关联的账单ID
    private int ownerId;            // 业主ID
    private BigDecimal paymentAmount; // 缴费金额
    private String paymentMethod;   // 缴费方式：现金、银行转账、在线支付等
    private Timestamp paymentTime;  // 缴费时间
    private String operator;        // 操作员
    private String receiptNo;       // 收据编号
    private String remark;          // 备注
    private Timestamp createTime;   // 创建时间
    private Timestamp updateTime;   // 更新时间
    
    // 非数据库字段，用于显示
    private String ownerName;       // 业主姓名
    private String billNo;          // 账单编号

    // 构造函数
    public PaymentRecord() {
    }

    public PaymentRecord(int id, String recordNo, int billId, int ownerId, BigDecimal paymentAmount,
                        String paymentMethod, Timestamp paymentTime, String operator, String receiptNo,
                        String remark) {
        this.id = id;
        this.recordNo = recordNo;
        this.billId = billId;
        this.ownerId = ownerId;
        this.paymentAmount = paymentAmount;
        this.paymentMethod = paymentMethod;
        this.paymentTime = paymentTime;
        this.operator = operator;
        this.receiptNo = receiptNo;
        this.remark = remark;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }

    public int getBillId() {
        return billId;
    }

    public void setBillId(int billId) {
        this.billId = billId;
    }

    public int getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(int ownerId) {
        this.ownerId = ownerId;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Timestamp getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(Timestamp paymentTime) {
        this.paymentTime = paymentTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    @Override
    public String toString() {
        return "PaymentRecord{" +
                "id=" + id +
                ", recordNo='" + recordNo + '\'' +
                ", billId=" + billId +
                ", ownerId=" + ownerId +
                ", paymentAmount=" + paymentAmount +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", paymentTime=" + paymentTime +
                ", operator='" + operator + '\'' +
                ", receiptNo='" + receiptNo + '\'' +
                ", remark='" + remark + '\'' +
                ", ownerName='" + ownerName + '\'' +
                ", billNo='" + billNo + '\'' +
                '}';
    }
}
