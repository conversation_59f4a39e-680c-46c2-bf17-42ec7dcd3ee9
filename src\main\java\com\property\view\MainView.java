package com.property.view;

import java.awt.Color;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.EventQueue;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.ImageIcon;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.SwingConstants;
import javax.swing.border.EmptyBorder;

import com.property.util.BackgroundPanel;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.ShadowPanel;

/**
 * 主界面
 */
public class MainView extends JFrame {

    private static final long serialVersionUID = 1L;
    private JPanel contentPane;
    private static MainView frame = new MainView();

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建主界面 - 现代化风格
     */
    public MainView() {
        setTitle("物业管理系统 - 主界面");
        setResizable(true);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 900, 600);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            // 使用准备好的图标文件
            ImageIcon icon = new ImageIcon(MainView.class.getResource("/imagine/图标.png"));
            setIconImage(icon.getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建背景图片面板
        contentPane = new BackgroundPanel("/imagine/主界面.png");
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);
        contentPane.setLayout(null);

        // 创建现代化菜单栏
        JMenuBar menuBar = new JMenuBar();
        menuBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        menuBar.setBorder(BorderFactory.createEmptyBorder());
        menuBar.setBounds(0, 0, 900, 50);
        menuBar.setLayout(new BoxLayout(menuBar, BoxLayout.X_AXIS));
        contentPane.add(menuBar);

        // 系统标题已移除，直接从账户设置开始

        // 账户设置菜单
        JMenu accountMenu = createMenu("账户设置");
        menuBar.add(accountMenu);

        // 修改密码菜单项
        JMenuItem changePasswordItem = createMenuItem("修改密码");
        changePasswordItem.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                new ChangePasswordView().setVisible(true);
            }
        });
        accountMenu.add(changePasswordItem);

        // 退出账号菜单项
        JMenuItem logoutItem = createMenuItem("退出账号");
        logoutItem.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                frame.dispose();
                new LoginView().setVisible(true);
                dispose();
            }
        });
        accountMenu.add(logoutItem);

        // 添加间隔
        menuBar.add(Box.createRigidArea(new Dimension(20, 0)));

        // 业主信息菜单 - 仅管理员可见
        JMenu ownerInfoMenu = createMenu("业主信息");
        ownerInfoMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                new OwnerInfoView().setVisible(true);
            }
        });
        // 仅当用户类型为管理员时添加此菜单
        if (LoginView.userType != null && LoginView.userType.equals("管理员")) {
            menuBar.add(ownerInfoMenu);
            // 添加间隔
            menuBar.add(Box.createRigidArea(new Dimension(20, 0)));
        }

        // 保修服务菜单 - 仅普通用户可见
        JMenu repairServiceMenu = createMenu("保修服务");
        repairServiceMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                new RepairServiceView().setVisible(true);
            }
        });
        // 仅当用户类型为普通用户时添加此菜单
        if (LoginView.userType != null && LoginView.userType.equals("用户")) {
            menuBar.add(repairServiceMenu);
            // 添加间隔
            menuBar.add(Box.createRigidArea(new Dimension(20, 0)));
        }

        // 报修请求菜单 - 仅管理员可见
        JMenu repairRequestMenu = createMenu("报修请求");
        repairRequestMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                new RepairRequestView().setVisible(true);
            }
        });
        // 仅当用户类型为管理员时添加此菜单
        if (LoginView.userType != null && LoginView.userType.equals("管理员")) {
            menuBar.add(repairRequestMenu);
            // 添加间隔
            menuBar.add(Box.createRigidArea(new Dimension(20, 0)));
        }

        // 缴费管理菜单 - 仅管理员可见
        JMenu paymentMenu = createMenu("缴费管理");
        paymentMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                new PaymentManagementView().setVisible(true);
            }
        });
        // 仅当用户类型为管理员时添加此菜单
        if (LoginView.userType != null && LoginView.userType.equals("管理员")) {
            menuBar.add(paymentMenu);
            // 添加间隔
            menuBar.add(Box.createRigidArea(new Dimension(20, 0)));
        }

        // 用户缴费查询菜单 - 仅普通用户可见
        JMenu userPaymentMenu = createMenu("缴费查询");
        userPaymentMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // 使用专门为用户设计的缴费查询界面
                new UserPaymentQueryView().setVisible(true);
            }
        });
        // 仅当用户类型为普通用户时添加此菜单
        if (LoginView.userType != null && LoginView.userType.equals("用户")) {
            menuBar.add(userPaymentMenu);
            // 添加间隔
            menuBar.add(Box.createRigidArea(new Dimension(20, 0)));
        }

        // 添加弹性空间，将用户信息推到右边
        menuBar.add(Box.createHorizontalGlue());

        // 显示当前用户
        JLabel userLabel = new JLabel("当前用户: " + (LoginView.currentUserName != null ? LoginView.currentUserName : "未登录"));
        userLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        userLabel.setForeground(Color.WHITE);
        userLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 20));
        menuBar.add(userLabel);

        // 内容区域 - 使用阴影面板
        ShadowPanel contentPanel = new ShadowPanel();
        contentPanel.setBounds(50, 70, 800, 450);
        contentPanel.setLayout(null);
        contentPane.add(contentPanel);

        // 欢迎信息 - 根据用户类型显示不同的欢迎信息
        String welcomeText = "欢迎使用物业管理系统";
        if (LoginView.userType != null && LoginView.currentUserName != null) {
            if (LoginView.userType.equals("管理员")) {
                welcomeText = "欢迎管理员 " + LoginView.currentUserName + " 使用物业管理系统";
            } else {
                welcomeText = "欢迎用户 " + LoginView.currentUserName + " 使用物业管理系统";
            }
        }
        JLabel welcomeLabel = new JLabel(welcomeText);
        welcomeLabel.setFont(new Font("微软雅黑", Font.BOLD, 24));
        welcomeLabel.setForeground(UIStyleUtil.PRIMARY_COLOR);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        welcomeLabel.setBounds(0, 30, 800, 40);
        contentPanel.add(welcomeLabel);

        // 系统说明 - 根据用户类型显示不同的说明
        String descriptionText = "请从上方菜单栏选择您需要的功能";
        if (LoginView.userType != null) {
            if (LoginView.userType.equals("管理员")) {
                descriptionText = "您可以管理业主信息、查看报修请求和管理缴费事务";
            } else {
                descriptionText = "您可以提交保修服务和查询缴费信息";
            }
        }
        JLabel descriptionLabel = new JLabel(descriptionText);
        descriptionLabel.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        descriptionLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        descriptionLabel.setHorizontalAlignment(SwingConstants.CENTER);
        descriptionLabel.setBounds(0, 80, 800, 30);
        contentPanel.add(descriptionLabel);

        // 内容区域的背景设置为半透明白色
        contentPanel.setBackground(new Color(255, 255, 255, 200));

    }

    /**
     * 创建现代化菜单
     */
    private JMenu createMenu(String text) {
        JMenu menu = new JMenu(text);
        menu.setFont(UIStyleUtil.MENU_FONT);
        menu.setForeground(Color.WHITE);
        menu.setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));

        // 设置弹出菜单的样式
        JPopupMenu popupMenu = menu.getPopupMenu();
        popupMenu.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        popupMenu.setBackground(Color.WHITE);

        // 将自定义UI应用到弹出菜单
        try {
            popupMenu.setUI(new UIStyleUtil.ModernPopupMenuUI());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 添加鼠标悬停效果
        menu.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                menu.setBackground(UIStyleUtil.MENU_HOVER_COLOR);
                menu.setCursor(new Cursor(Cursor.HAND_CURSOR));
            }

            @Override
            public void mouseExited(MouseEvent e) {
                menu.setBackground(UIStyleUtil.PRIMARY_COLOR);
                menu.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
            }
        });

        return menu;
    }

    /**
     * 创建现代化菜单项
     */
    private JMenuItem createMenuItem(String text) {
        JMenuItem menuItem = new JMenuItem(text);
        menuItem.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        menuItem.setBackground(Color.WHITE);
        menuItem.setForeground(UIStyleUtil.TEXT_COLOR);
        menuItem.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15)); // 增加内边距

        // 将自定义UI应用到菜单项
        try {
            menuItem.setUI(new UIStyleUtil.ModernMenuItemRenderer());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 添加鼠标悬停效果
        menuItem.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                menuItem.setCursor(new Cursor(Cursor.HAND_CURSOR));
            }

            @Override
            public void mouseExited(MouseEvent e) {
                menuItem.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
            }
        });

        return menuItem;
    }
}
