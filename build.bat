@echo off
echo 正在构建物业管理系统...
echo.

rem 创建目录结构（如果不存在）
if not exist target\classes mkdir target\classes
if not exist lib mkdir lib
if not exist dist mkdir dist

rem 清理旧的编译文件
echo 清理旧的编译文件...
del /Q /S target\classes\*

rem 编译源代码
echo 编译源代码...
javac -d target\classes -cp ".;lib\*;jdbc\*" src\main\java\com\property\app.java src\main\java\com\property\view\*.java src\main\java\com\property\dao\*.java src\main\java\com\property\model\*.java src\main\java\com\property\util\*.java src\main\java\module-info.java

rem 复制资源文件
echo 复制资源文件...
xcopy /Y /E /I src\main\resources\imagine target\classes\imagine

rem 创建JAR文件
echo 创建JAR文件...
jar cfm dist\PropertyManagementSystem.jar MANIFEST.MF -C target\classes .

rem 复制运行所需的文件到dist目录
echo 复制运行所需的文件...
xcopy /Y run.bat dist\
xcopy /Y /E /I lib dist\lib\
xcopy /Y /E /I jdbc dist\jdbc\

echo.
echo 构建完成！
echo 可以通过双击dist\PropertyManagementSystem.jar或运行dist\run.bat来启动应用程序。
pause
