package com.property.model;

/**
 * 账户信息模型类
 */
public class Account {
    private int id;
    private String userName;
    private String password;
    
    /**
     * 默认构造函数
     */
    public Account() {
        super();
    }
    
    /**
     * 带用户名和密码的构造函数
     * @param userName 用户名
     * @param password 密码
     */
    public Account(String userName, String password) {
        super();
        this.userName = userName;
        this.password = password;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    // 为了兼容旧代码，保留以下方法
    public String getPassWord() {
        return password;
    }
    
    public void setPassWord(String passWord) {
        this.password = passWord;
    }
}
