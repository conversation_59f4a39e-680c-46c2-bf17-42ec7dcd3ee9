package com.property.dao;

import com.property.model.PaymentBill;
import com.property.util.DatabaseConfig;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 账单数据访问对象
 * 负责与t_payment_bill表的交互
 */
public class PaymentBillDAO {

    /**
     * 获取所有账单
     * @return 账单列表
     */
    public List<PaymentBill> getAllBills() {
        List<PaymentBill> bills = new ArrayList<>();
        String sql = "SELECT b.*, o.name AS owner_name, f.name AS fee_type_name " +
                     "FROM t_payment_bill b " +
                     "JOIN t_owner_info o ON b.owner_id = o.id " +
                     "JOIN t_fee_type f ON b.fee_type_id = f.id " +
                     "ORDER BY b.create_time DESC";

        try (Connection conn = DatabaseConfig.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                PaymentBill bill = extractBillFromResultSet(rs);
                bills.add(bill);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return bills;
    }

    /**
     * 根据业主ID获取账单
     * @param ownerId 业主ID
     * @return 账单列表
     */
    public List<PaymentBill> getBillsByOwnerId(int ownerId) {
        List<PaymentBill> bills = new ArrayList<>();
        String sql = "SELECT b.*, o.name AS owner_name, f.name AS fee_type_name " +
                     "FROM t_payment_bill b " +
                     "JOIN t_owner_info o ON b.owner_id = o.id " +
                     "JOIN t_fee_type f ON b.fee_type_id = f.id " +
                     "WHERE b.owner_id = ? " +
                     "ORDER BY b.create_time DESC";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, ownerId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    PaymentBill bill = extractBillFromResultSet(rs);
                    bills.add(bill);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return bills;
    }

    /**
     * 根据状态获取账单
     * @param status 账单状态
     * @return 账单列表
     */
    public List<PaymentBill> getBillsByStatus(String status) {
        List<PaymentBill> bills = new ArrayList<>();
        String sql = "SELECT b.*, o.name AS owner_name, f.name AS fee_type_name " +
                     "FROM t_payment_bill b " +
                     "JOIN t_owner_info o ON b.owner_id = o.id " +
                     "JOIN t_fee_type f ON b.fee_type_id = f.id " +
                     "WHERE b.status = ? " +
                     "ORDER BY b.create_time DESC";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, status);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    PaymentBill bill = extractBillFromResultSet(rs);
                    bills.add(bill);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return bills;
    }

    /**
     * 获取欠费账单（未缴费和逾期）
     * @return 欠费账单列表
     */
    public List<PaymentBill> getOverdueBills() {
        List<PaymentBill> bills = new ArrayList<>();
        String sql = "SELECT b.*, o.name AS owner_name, f.name AS fee_type_name " +
                     "FROM t_payment_bill b " +
                     "JOIN t_owner_info o ON b.owner_id = o.id " +
                     "JOIN t_fee_type f ON b.fee_type_id = f.id " +
                     "WHERE b.status IN ('未缴费', '部分缴费') " +
                     "ORDER BY b.due_date ASC";

        try (Connection conn = DatabaseConfig.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                PaymentBill bill = extractBillFromResultSet(rs);
                bills.add(bill);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return bills;
    }

    /**
     * 根据ID获取账单
     * @param id 账单ID
     * @return 账单对象，如果不存在则返回null
     */
    public PaymentBill getBillById(int id) {
        String sql = "SELECT b.*, o.name AS owner_name, f.name AS fee_type_name " +
                     "FROM t_payment_bill b " +
                     "JOIN t_owner_info o ON b.owner_id = o.id " +
                     "JOIN t_fee_type f ON b.fee_type_id = f.id " +
                     "WHERE b.id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return extractBillFromResultSet(rs);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 添加账单
     * @param bill 账单对象
     * @return 添加成功返回生成的ID，失败返回-1
     */
    public int addBill(PaymentBill bill) {
        String sql = "INSERT INTO t_payment_bill (bill_no, owner_id, fee_type_id, billing_period, " +
                     "amount, paid_amount, status, due_date, remark) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, bill.getBillNo());
            pstmt.setInt(2, bill.getOwnerId());
            pstmt.setInt(3, bill.getFeeTypeId());
            pstmt.setString(4, bill.getBillingPeriod());
            pstmt.setBigDecimal(5, bill.getAmount());
            pstmt.setBigDecimal(6, bill.getPaidAmount() != null ? bill.getPaidAmount() : BigDecimal.ZERO);
            pstmt.setString(7, bill.getStatus());
            pstmt.setDate(8, bill.getDueDate());
            pstmt.setString(9, bill.getRemark());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        return generatedKeys.getInt(1);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return -1;
    }

    /**
     * 更新账单
     * @param bill 账单对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateBill(PaymentBill bill) {
        String sql = "UPDATE t_payment_bill SET owner_id = ?, fee_type_id = ?, billing_period = ?, " +
                     "amount = ?, paid_amount = ?, status = ?, due_date = ?, remark = ? " +
                     "WHERE id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, bill.getOwnerId());
            pstmt.setInt(2, bill.getFeeTypeId());
            pstmt.setString(3, bill.getBillingPeriod());
            pstmt.setBigDecimal(4, bill.getAmount());
            pstmt.setBigDecimal(5, bill.getPaidAmount());
            pstmt.setString(6, bill.getStatus());
            pstmt.setDate(7, bill.getDueDate());
            pstmt.setString(8, bill.getRemark());
            pstmt.setInt(9, bill.getId());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新账单状态和已缴金额
     * @param id 账单ID
     * @param paidAmount 已缴金额
     * @param status 状态
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateBillPayment(int id, BigDecimal paidAmount, String status) {
        String sql = "UPDATE t_payment_bill SET paid_amount = ?, status = ? WHERE id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setBigDecimal(1, paidAmount);
            pstmt.setString(2, status);
            pstmt.setInt(3, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除账单
     * @param id 账单ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteBill(int id) {
        String sql = "DELETE FROM t_payment_bill WHERE id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成账单编号
     * @return 账单编号
     */
    public String generateBillNo() {
        return "PB" + System.currentTimeMillis();
    }

    /**
     * 从ResultSet中提取账单对象
     * @param rs ResultSet
     * @return 账单对象
     * @throws SQLException SQL异常
     */
    private PaymentBill extractBillFromResultSet(ResultSet rs) throws SQLException {
        PaymentBill bill = new PaymentBill();
        bill.setId(rs.getInt("id"));
        bill.setBillNo(rs.getString("bill_no"));
        bill.setOwnerId(rs.getInt("owner_id"));
        bill.setFeeTypeId(rs.getInt("fee_type_id"));
        bill.setBillingPeriod(rs.getString("billing_period"));
        bill.setAmount(rs.getBigDecimal("amount"));
        bill.setPaidAmount(rs.getBigDecimal("paid_amount"));
        bill.setStatus(rs.getString("status"));
        bill.setDueDate(rs.getDate("due_date"));
        bill.setRemark(rs.getString("remark"));
        bill.setCreateTime(rs.getTimestamp("create_time"));
        bill.setUpdateTime(rs.getTimestamp("update_time"));
        bill.setOwnerName(rs.getString("owner_name"));
        bill.setFeeTypeName(rs.getString("fee_type_name"));
        return bill;
    }
}
