package com.property.view;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import com.property.util.UIStyleUtil;

/**
 * 缴费管理主界面
 * 作为缴费管理功能的入口，提供各个子功能的访问
 */
public class PaymentManagementView extends JFrame {

    private JButton feeTypeButton;
    private JButton billManagementButton;
    private JButton recordManagementButton;

    public PaymentManagementView() {
        initComponents();
    }

    private void initComponents() {
        setTitle("缴费管理");
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);

        // 创建标题面板
        JPanel titlePanel = new JPanel();
        titlePanel.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titlePanel.setPreferredSize(new Dimension(800, 80));
        titlePanel.setLayout(new BorderLayout());

        JLabel titleLabel = new JLabel("物业管理系统 - 缴费管理", JLabel.CENTER);
        titleLabel.setFont(UIStyleUtil.TITLE_FONT);
        titleLabel.setForeground(UIStyleUtil.TITLE_COLOR);
        titlePanel.add(titleLabel, BorderLayout.CENTER);

        // 创建功能按钮面板
        JPanel buttonPanel = new UIStyleUtil.ShadowPanel();
        buttonPanel.setLayout(new GridLayout(2, 2, 20, 20));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(30, 50, 30, 50));

        feeTypeButton = createFunctionButton("费用类型管理", "管理各种费用类型，如物业费、水电费等");
        billManagementButton = createFunctionButton("账单管理", "生成和管理业主的账单");
        recordManagementButton = createFunctionButton("缴费记录管理", "查看和管理业主的缴费记录");

        buttonPanel.add(feeTypeButton);
        buttonPanel.add(billManagementButton);
        buttonPanel.add(recordManagementButton);

        // 创建说明面板
        JPanel descriptionPanel = new UIStyleUtil.ShadowPanel();
        descriptionPanel.setLayout(new BorderLayout());
        descriptionPanel.setBorder(UIStyleUtil.createRoundedBorder(UIStyleUtil.SECONDARY_COLOR, 10, 10));

        JTextArea descriptionArea = new JTextArea();
        descriptionArea.setText("缴费管理模块是物业管理系统的核心功能之一，用于管理业主的各类费用缴纳，包括物业费、水电费、停车费等。\n\n" +
                               "主要功能包括：\n" +
                               "1. 费用类型管理：添加、修改、删除各种费用类型\n" +
                               "2. 账单管理：生成账单、查看账单详情、处理缴费\n" +
                               "3. 缴费记录管理：查看缴费记录、打印收据\n\n" +
                               "请点击上方按钮进入相应功能。");
        descriptionArea.setEditable(false);
        descriptionArea.setBackground(Color.WHITE);
        descriptionArea.setFont(UIStyleUtil.LABEL_FONT);
        descriptionArea.setForeground(UIStyleUtil.TEXT_COLOR);
        descriptionArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);

        JScrollPane scrollPane = new JScrollPane(descriptionArea);
        descriptionPanel.add(scrollPane, BorderLayout.CENTER);

        // 设置布局
        setLayout(new BorderLayout());
        add(titlePanel, BorderLayout.NORTH);
        add(buttonPanel, BorderLayout.CENTER);
        add(descriptionPanel, BorderLayout.SOUTH);

        // 添加事件监听器
        feeTypeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openFeeTypeManagement();
            }
        });

        billManagementButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openBillManagement();
            }
        });

        recordManagementButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                openRecordManagement();
            }
        });

        // 缴费统计和逾期检查功能已移除
    }

    /**
     * 创建功能按钮
     * @param text 按钮文本
     * @param tooltip 提示文本
     * @return 按钮
     */
    private JButton createFunctionButton(String text, String tooltip) {
        UIStyleUtil.RoundedButton button = new UIStyleUtil.RoundedButton(text, UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        button.setFont(UIStyleUtil.BUTTON_FONT);
        button.setToolTipText(tooltip);
        button.setPreferredSize(new Dimension(200, 80));

        return button;
    }

    /**
     * 打开费用类型管理界面
     */
    private void openFeeTypeManagement() {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new FeeTypeManagementView().setVisible(true);
            }
        });
    }

    /**
     * 打开账单管理界面
     */
    private void openBillManagement() {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new PaymentBillManagementView().setVisible(true);
            }
        });
    }

    /**
     * 打开缴费记录管理界面
     */
    private void openRecordManagement() {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new PaymentRecordManagementView().setVisible(true);
            }
        });
    }

    // 缴费统计和逾期检查功能已移除

    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                new PaymentManagementView().setVisible(true);
            }
        });
    }
}
