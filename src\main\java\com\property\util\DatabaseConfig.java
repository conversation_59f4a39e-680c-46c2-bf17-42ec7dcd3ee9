package com.property.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库配置类
 * 集中管理数据库连接信息
 */
public class DatabaseConfig {
    // 数据库连接URL
    public static final String JDBC_URL = "****************************************************************************************************";

    // 数据库用户名
    public static final String USERNAME = "root";

    // 数据库密码
    public static final String PASSWORD = "Yf.31306";

    // JDBC驱动类名
    public static final String DRIVER_NAME = "com.mysql.cj.jdbc.Driver";

    // 数据库名称
    public static final String DATABASE_NAME = "mysql_assignment";

    // 数据库端口
    public static final int PORT = 3306;

    // 数据库主机
    public static final String HOST = "localhost";

    // 静态代码块，加载JDBC驱动
    static {
        try {
            Class.forName(DRIVER_NAME);
        } catch (ClassNotFoundException e) {
            System.err.println("加载JDBC驱动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取数据库连接
     * @return 数据库连接
     * @throws SQLException SQL异常
     */
    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD);
    }

    /**
     * 关闭数据库连接
     * @param conn 数据库连接
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭数据库连接失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取数据库连接URL
     * @return 数据库连接URL
     */
    public static String getJdbcUrl() {
        return JDBC_URL;
    }

    /**
     * 获取数据库用户名
     * @return 数据库用户名
     */
    public static String getUsername() {
        return USERNAME;
    }

    /**
     * 获取数据库密码
     * @return 数据库密码
     */
    public static String getPassword() {
        return PASSWORD;
    }

    /**
     * 获取JDBC驱动类名
     * @return JDBC驱动类名
     */
    public static String getDriverName() {
        return DRIVER_NAME;
    }
}
