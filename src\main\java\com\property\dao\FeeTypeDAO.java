package com.property.dao;

import com.property.model.FeeType;
import com.property.util.DatabaseConfig;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 费用类型数据访问对象
 * 负责与t_fee_type表的交互
 */
public class FeeTypeDAO {

    /**
     * 获取所有费用类型
     * @return 费用类型列表
     */
    public List<FeeType> getAllFeeTypes() {
        List<FeeType> feeTypes = new ArrayList<>();
        String sql = "SELECT * FROM t_fee_type ORDER BY id";

        try (Connection conn = DatabaseConfig.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                FeeType feeType = new FeeType();
                feeType.setId(rs.getInt("id"));
                feeType.setName(rs.getString("name"));
                feeType.setDescription(rs.getString("description"));
                feeType.setUnitPrice(rs.getBigDecimal("unit_price"));
                feeType.setBillingCycle(rs.getString("billing_cycle"));
                feeType.setCalculationMethod(rs.getString("calculation_method"));
                feeType.setActive(rs.getBoolean("is_active"));
                feeType.setCreateTime(rs.getTimestamp("create_time"));
                feeType.setUpdateTime(rs.getTimestamp("update_time"));
                feeTypes.add(feeType);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return feeTypes;
    }

    /**
     * 获取所有启用的费用类型
     * @return 启用的费用类型列表
     */
    public List<FeeType> getActiveFeeTypes() {
        List<FeeType> feeTypes = new ArrayList<>();
        String sql = "SELECT * FROM t_fee_type WHERE is_active = 1 ORDER BY id";

        try (Connection conn = DatabaseConfig.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                FeeType feeType = new FeeType();
                feeType.setId(rs.getInt("id"));
                feeType.setName(rs.getString("name"));
                feeType.setDescription(rs.getString("description"));
                feeType.setUnitPrice(rs.getBigDecimal("unit_price"));
                feeType.setBillingCycle(rs.getString("billing_cycle"));
                feeType.setCalculationMethod(rs.getString("calculation_method"));
                feeType.setActive(rs.getBoolean("is_active"));
                feeType.setCreateTime(rs.getTimestamp("create_time"));
                feeType.setUpdateTime(rs.getTimestamp("update_time"));
                feeTypes.add(feeType);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return feeTypes;
    }

    /**
     * 根据ID获取费用类型
     * @param id 费用类型ID
     * @return 费用类型对象，如果不存在则返回null
     */
    public FeeType getFeeTypeById(int id) {
        String sql = "SELECT * FROM t_fee_type WHERE id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    FeeType feeType = new FeeType();
                    feeType.setId(rs.getInt("id"));
                    feeType.setName(rs.getString("name"));
                    feeType.setDescription(rs.getString("description"));
                    feeType.setUnitPrice(rs.getBigDecimal("unit_price"));
                    feeType.setBillingCycle(rs.getString("billing_cycle"));
                    feeType.setCalculationMethod(rs.getString("calculation_method"));
                    feeType.setActive(rs.getBoolean("is_active"));
                    feeType.setCreateTime(rs.getTimestamp("create_time"));
                    feeType.setUpdateTime(rs.getTimestamp("update_time"));
                    return feeType;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 添加费用类型
     * @param feeType 费用类型对象
     * @return 添加成功返回true，否则返回false
     */
    public boolean addFeeType(FeeType feeType) {
        String sql = "INSERT INTO t_fee_type (name, description, unit_price, billing_cycle, calculation_method, is_active) " +
                     "VALUES (?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, feeType.getName());
            pstmt.setString(2, feeType.getDescription());
            pstmt.setBigDecimal(3, feeType.getUnitPrice());
            pstmt.setString(4, feeType.getBillingCycle());
            pstmt.setString(5, feeType.getCalculationMethod());
            pstmt.setBoolean(6, feeType.isActive());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新费用类型
     * @param feeType 费用类型对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateFeeType(FeeType feeType) {
        String sql = "UPDATE t_fee_type SET name = ?, description = ?, unit_price = ?, " +
                     "billing_cycle = ?, calculation_method = ?, is_active = ? WHERE id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, feeType.getName());
            pstmt.setString(2, feeType.getDescription());
            pstmt.setBigDecimal(3, feeType.getUnitPrice());
            pstmt.setString(4, feeType.getBillingCycle());
            pstmt.setString(5, feeType.getCalculationMethod());
            pstmt.setBoolean(6, feeType.isActive());
            pstmt.setInt(7, feeType.getId());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除费用类型
     * @param id 费用类型ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteFeeType(int id) {
        String sql = "DELETE FROM t_fee_type WHERE id = ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查费用类型名称是否已存在
     * @param name 费用类型名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在返回true，否则返回false
     */
    public boolean isFeeTypeNameExists(String name, int excludeId) {
        String sql = "SELECT COUNT(*) FROM t_fee_type WHERE name = ? AND id != ?";

        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, name);
            pstmt.setInt(2, excludeId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return false;
    }
}
