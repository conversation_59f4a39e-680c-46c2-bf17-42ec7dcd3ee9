package com.property.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 费用类型模型类
 * 对应数据库中的t_fee_type表
 */
public class FeeType {
    private int id;                 // 主键ID
    private String name;            // 费用类型名称
    private String description;     // 费用描述
    private BigDecimal unitPrice;   // 单价
    private String billingCycle;    // 计费周期：月度、季度、年度
    private String calculationMethod; // 计费方式：固定金额、按面积、按用量等
    private boolean isActive;       // 是否启用
    private Timestamp createTime;   // 创建时间
    private Timestamp updateTime;   // 更新时间

    // 构造函数
    public FeeType() {
    }

    public FeeType(int id, String name, String description, BigDecimal unitPrice, 
                  String billingCycle, String calculationMethod, boolean isActive) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.unitPrice = unitPrice;
        this.billingCycle = billingCycle;
        this.calculationMethod = calculationMethod;
        this.isActive = isActive;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(String billingCycle) {
        this.billingCycle = billingCycle;
    }

    public String getCalculationMethod() {
        return calculationMethod;
    }

    public void setCalculationMethod(String calculationMethod) {
        this.calculationMethod = calculationMethod;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FeeType{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", unitPrice=" + unitPrice +
                ", billingCycle='" + billingCycle + '\'' +
                ", calculationMethod='" + calculationMethod + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
