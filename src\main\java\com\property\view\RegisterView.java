package com.property.view;

import java.awt.Color;
import java.awt.EventQueue;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.ImageIcon;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuBar;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;

import com.property.util.DialogUtil;
import com.property.util.OwnerDataService;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.GradientPanel;
import com.property.util.UIStyleUtil.RoundedButton;
import com.property.util.UIStyleUtil.ShadowPanel;

/**
 * 用户注册界面
 */
public class RegisterView extends JFrame {

    private static final long serialVersionUID = 1L;
    private GradientPanel contentPane;
    private JTextField userNameField;
    private JPasswordField passwordField;
    private JLabel userNameLabel;
    private JLabel passwordLabel;
    private static RegisterView frame = new RegisterView();

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建注册界面 - 现代化风格
     */
    public RegisterView() {
        setTitle("注册信息");
        setResizable(false);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 500, 400);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            // 使用准备好的图标文件
            ImageIcon icon = new ImageIcon(RegisterView.class.getResource("/imagine/图标.png"));
            setIconImage(icon.getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        contentPane = new GradientPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);
        contentPane.setLayout(null);

        // 创建现代化菜单栏作为标题栏
        JMenuBar titleBar = new JMenuBar();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setBorder(BorderFactory.createEmptyBorder());
        titleBar.setBounds(0, 0, 500, 50);
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));
        contentPane.add(titleBar);

        // 添加系统标题
        JLabel titleLabel = new JLabel("物业管理系统");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 40));
        titleBar.add(titleLabel);

        // 添加弹性空间，将注册信息推到右边
        titleBar.add(Box.createHorizontalGlue());

        // 显示注册标识
        JLabel registerLabel = new JLabel("用户注册");
        registerLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        registerLabel.setForeground(Color.WHITE);
        registerLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 20));
        titleBar.add(registerLabel);

        // 注册表单面板 - 使用带阴影的面板
        ShadowPanel formPanelContainer = new ShadowPanel();
        formPanelContainer.setBounds(50, 80, 400, 240);
        formPanelContainer.setLayout(null);
        contentPane.add(formPanelContainer);

        JPanel formPanel = new JPanel();
        formPanel.setOpaque(false);
        formPanel.setBounds(5, 5, 390, 230);
        formPanel.setLayout(null);
        formPanelContainer.add(formPanel);

        // 用户名标签
        userNameLabel = new JLabel("用户名");
        userNameLabel.setFont(UIStyleUtil.LABEL_FONT);
        userNameLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        userNameLabel.setBounds(30, 30, 100, 30);
        formPanel.add(userNameLabel);

        // 用户名输入框
        userNameField = new JTextField();
        userNameField.setBounds(140, 30, 220, 35);
        UIStyleUtil.styleTextField(userNameField);
        formPanel.add(userNameField);

        // 密码标签
        passwordLabel = new JLabel("密码");
        passwordLabel.setFont(UIStyleUtil.LABEL_FONT);
        passwordLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        passwordLabel.setBounds(30, 80, 100, 30);
        formPanel.add(passwordLabel);

        // 密码输入框
        passwordField = new JPasswordField();
        passwordField.setBounds(140, 80, 220, 35);
        UIStyleUtil.styleTextField(passwordField);
        formPanel.add(passwordField);

        // 确认按钮
        RoundedButton confirmButton = new RoundedButton("确认注册", UIStyleUtil.PRIMARY_COLOR, UIStyleUtil.SECONDARY_COLOR);
        confirmButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                registerUser(e);
            }
        });
        confirmButton.setBounds(140, 140, 100, 40);
        formPanel.add(confirmButton);

        // 清空按钮
        RoundedButton clearButton = new RoundedButton("清空", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        clearButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                userNameField.setText("");
                passwordField.setText("");
            }
        });
        clearButton.setBounds(260, 140, 100, 40);
        formPanel.add(clearButton);

    }

    /**
     * 注册用户
     */
    private void registerUser(ActionEvent e) {
        // 获取用户名和密码
        String userName = userNameField.getText();
        String password = new String(passwordField.getPassword());

        // 输入验证
        if (userName.trim().isEmpty()) {
            DialogUtil.showWarning(this, "用户名不能为空");
            return;
        }

        if (password.trim().isEmpty()) {
            DialogUtil.showWarning(this, "密码不能为空");
            return;
        }

        // 创建业主数据服务对象
        OwnerDataService ownerDataService = new OwnerDataService(userName, password);

        // 插入用户账户信息
        boolean success = ownerDataService.insertUserAccount();

        if (success) {
            DialogUtil.showSuccess(this, "注册成功！请返回登录页面");
            dispose(); // 关闭注册窗口
        }
    }
}
