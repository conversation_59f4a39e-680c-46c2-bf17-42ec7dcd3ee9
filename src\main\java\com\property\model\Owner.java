package com.property.model;

/**
 * 业主信息模型类
 */
public class Owner {
    private int id; // 业主ID
    private String name;
    private String sex;
    private String houseNumber; // 门牌号
    private String repairContent; // 报修内容
    private String password;
    private String phone;
    private String idNumber; // 身份证号
    private String remark; // 备注信息
    private String emergencyContact; // 紧急联系人
    private String emergencyPhone; // 紧急联系人电话

    /**
     * 默认构造函数
     */
    public Owner() {
    }

    /**
     * 用于登录验证的构造函数
     * @param name 用户名
     * @param password 密码
     */
    public Owner(String name, String password) {
        this.name = name;
        this.password = password;
    }

    /**
     * 用于报修服务的构造函数
     * @param name 业主姓名
     * @param houseNumber 门牌号
     * @param repairContent 报修内容
     */
    public Owner(String name, String houseNumber, String repairContent) {
        this.name = name;
        this.houseNumber = houseNumber;
        this.repairContent = repairContent;
    }

    /**
     * 基本业主信息的构造函数
     * @param name 业主姓名
     * @param sex 性别
     * @param houseNumber 门牌号
     * @param phone 联系电话
     * @param idNumber 身份证号
     */
    public Owner(String name, String sex, String houseNumber, String phone, String idNumber) {
        this.name = name;
        this.sex = sex;
        this.houseNumber = houseNumber;
        this.phone = phone;
        this.idNumber = idNumber;
    }

    /**
     * 完整业主信息的构造函数
     * @param name 业主姓名
     * @param sex 性别
     * @param houseNumber 门牌号
     * @param phone 联系电话
     * @param idNumber 身份证号
     * @param remark 备注信息
     * @param emergencyContact 紧急联系人
     * @param emergencyPhone 紧急联系人电话
     */
    public Owner(String name, String sex, String houseNumber, String phone, String idNumber,
                String remark, String emergencyContact, String emergencyPhone) {
        this.name = name;
        this.sex = sex;
        this.houseNumber = houseNumber;
        this.phone = phone;
        this.idNumber = idNumber;
        this.remark = remark;
        this.emergencyContact = emergencyContact;
        this.emergencyPhone = emergencyPhone;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber = houseNumber;
    }

    public String getRepairContent() {
        return repairContent;
    }

    public void setRepairContent(String repairContent) {
        this.repairContent = repairContent;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    // 为了兼容旧代码，保留以下方法
    public String getMph() {
        return houseNumber;
    }

    public void setMph(String mph) {
        this.houseNumber = mph;
    }

    public String getBxnr() {
        return repairContent;
    }

    public void setBxnr(String bxnr) {
        this.repairContent = bxnr;
    }

    public String getPassward() {
        return password;
    }

    public void setPassward(String passward) {
        this.password = passward;
    }

    public String getIdnumber() {
        return idNumber;
    }

    public void setIdnumber(String idnumber) {
        this.idNumber = idnumber;
    }

    // 新增字段的 getter 和 setter
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEmergencyContact() {
        return emergencyContact;
    }

    public void setEmergencyContact(String emergencyContact) {
        this.emergencyContact = emergencyContact;
    }

    public String getEmergencyPhone() {
        return emergencyPhone;
    }

    public void setEmergencyPhone(String emergencyPhone) {
        this.emergencyPhone = emergencyPhone;
    }
}
