package com.property.view;

import java.awt.Color;
import java.awt.Component;
import java.awt.EventQueue;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.ResultSet;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuBar;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;

import com.property.dao.OwnerDao;
import com.property.model.Owner;
import com.property.util.DatabaseConnection;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.GradientPanel;
import com.property.util.UIStyleUtil.ShadowPanel;
import com.property.util.UIStyleUtil.RoundedButton;

/**
 * 业主信息界面
 */
public class OwnerInfoView extends JFrame {

    private static final long serialVersionUID = 1L;
    private JPanel contentPane;
    private JTable table;
    private JLabel nameLabel;
    private JTextField nameField;
    private JButton queryButton;
    private JButton addButton;
    private JButton deleteButton;

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    OwnerInfoView frame = new OwnerInfoView();
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建业主信息界面
     */
    public OwnerInfoView() {
        setTitle("业主信息");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 800, 600);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        contentPane = new GradientPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));

        setContentPane(contentPane);
        contentPane.setLayout(new BorderLayout());

        // 创建现代化菜单栏作为标题栏
        JMenuBar titleBar = new JMenuBar();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setBorder(BorderFactory.createEmptyBorder());
        titleBar.setPreferredSize(new Dimension(800, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));
        contentPane.add(titleBar, BorderLayout.NORTH);

        // 添加系统标题
        JLabel titleLabel = new JLabel("业主信息管理");
        titleLabel.setFont(UIStyleUtil.MENU_FONT);
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 0));
        titleBar.add(titleLabel);

        // 添加弹性空间，将登录信息推到右边
        titleBar.add(Box.createHorizontalGlue());

        // 创建内容面板
        JPanel mainPanel = new JPanel();
        mainPanel.setOpaque(false);
        mainPanel.setLayout(new BorderLayout());
        contentPane.add(mainPanel, BorderLayout.CENTER);

        // 创建带阴影的面板容器
        ShadowPanel formPanelContainer = new ShadowPanel();
        formPanelContainer.setLayout(new BorderLayout());
        formPanelContainer.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.add(formPanelContainer, BorderLayout.CENTER);

        // 创建内容面板
        JPanel formPanel = new JPanel();
        formPanel.setOpaque(false);
        formPanel.setLayout(new BorderLayout(0, 10));
        formPanelContainer.add(formPanel, BorderLayout.CENTER);

        JScrollPane scrollPane = new JScrollPane();

        // 创建搜索区域
        JPanel searchPanel = new JPanel();
        searchPanel.setOpaque(false);
        searchPanel.setPreferredSize(new Dimension(750, 60));
        searchPanel.setLayout(new FlowLayout(FlowLayout.LEFT, 10, 15));
        formPanel.add(searchPanel, BorderLayout.NORTH);

        // 姓名标签
        nameLabel = new JLabel("姓名：");
        nameLabel.setFont(UIStyleUtil.LABEL_FONT);
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        nameLabel.setPreferredSize(new Dimension(70, 30));
        searchPanel.add(nameLabel);

        // 姓名输入框
        nameField = new JTextField();
        nameField.setPreferredSize(new Dimension(150, 30));
        UIStyleUtil.styleTextField(nameField);
        searchPanel.add(nameField);

        // 查询按钮 - 使用自定义圆角按钮
        queryButton = new RoundedButton("查询", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        queryButton.setPreferredSize(new Dimension(100, 30));
        queryButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                queryOwnerInfo();
            }
        });
        searchPanel.add(queryButton);

        // 添加按钮 - 使用自定义圆角按钮
        addButton = new RoundedButton("添加", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        addButton.setPreferredSize(new Dimension(100, 30));
        addButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                // 传递当前视图实例，以便添加成功后刷新列表
                new AddOwnerView(OwnerInfoView.this).setVisible(true);
            }
        });
        searchPanel.add(addButton);

        // 删除按钮 - 使用自定义圆角按钮
        deleteButton = new RoundedButton("删除", new Color(231, 76, 60), UIStyleUtil.SECONDARY_COLOR);
        deleteButton.setPreferredSize(new Dimension(100, 30));
        deleteButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                deleteOwnerInfo();
            }
        });
        searchPanel.add(deleteButton);

        // 创建表格面板
        JPanel tablePanel = new JPanel();
        tablePanel.setOpaque(false);
        tablePanel.setLayout(new BorderLayout(0, 10));
        formPanel.add(tablePanel, BorderLayout.CENTER);

        // 表格标题
        JLabel tableTitle = new JLabel("业主信息列表");
        tableTitle.setFont(new Font("微软雅黑", Font.BOLD, 16));
        tableTitle.setForeground(UIStyleUtil.PRIMARY_COLOR);
        tableTitle.setBorder(BorderFactory.createEmptyBorder(0, 0, 10, 0));
        tablePanel.add(tableTitle, BorderLayout.NORTH);

        // 表格滚动面板
        scrollPane = new JScrollPane();
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        scrollPane.getViewport().setBackground(Color.WHITE);
        tablePanel.add(scrollPane, BorderLayout.CENTER);

        // 表格设置
        table = new JTable();
        table.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        table.setRowHeight(30);
        table.setShowGrid(false);
        table.setIntercellSpacing(new Dimension(0, 0));
        table.setFillsViewportHeight(true);

        // 设置表格模型
        table.setModel(new DefaultTableModel(
            new Object[][] {},
            new String[] {
                "业主ID", "业主姓名", "性别", "门牌号", "联系电话", "身份证号", "备注信息", "紧急联系人", "紧急电话"
            }
        ) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 使表格不可编辑
            }
        });

        // 设置表格条纹效果
        table.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (isSelected) {
                    c.setBackground(new Color(232, 242, 254));
                    c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                } else {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 247, 250));
                    }
                    c.setForeground(UIStyleUtil.TEXT_COLOR);
                }

                ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                // 业主ID和业主姓名列居中显示，其他列左对齐
                ((JLabel) c).setHorizontalAlignment(column == 0 || column == 1 ? JLabel.CENTER : JLabel.LEFT);

                return c;
            }
        });

        // 设置表头样式
        table.getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 14));
        table.getTableHeader().setBackground(UIStyleUtil.PRIMARY_COLOR);
        table.getTableHeader().setForeground(Color.WHITE);
        table.getTableHeader().setReorderingAllowed(false);
        table.getTableHeader().setResizingAllowed(true);

        scrollPane.setViewportView(table);

        // 初始化时加载所有业主信息
        queryOwnerInfo();
    }

    /**
     * 查询业主信息
     */
    public void queryOwnerInfo() {
        DatabaseConnection dbConnection = new DatabaseConnection();
        Owner owner = new Owner();
        OwnerDao ownerDao = new OwnerDao();
        ResultSet resultSet = null;

        // 设置查询条件
        owner.setName(nameField.getText());
        Connection connection = null;

        // 创建不可编辑的表格模型
        DefaultTableModel defaultTableModel = new DefaultTableModel() {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 使表格不可编辑
            }
        };

        try {
            connection = dbConnection.getConnection();
            resultSet = ownerDao.queryOwnerByName(connection, owner);

            // 获取列数
            int columnCount = resultSet.getMetaData().getColumnCount();

            // 添加列名
            String[] columnNames = {"业主ID", "业主姓名", "性别", "门牌号", "联系电话", "身份证号", "备注信息", "紧急联系人", "紧急电话"};
            for (String columnName : columnNames) {
                defaultTableModel.addColumn(columnName);
            }

            // 添加数据行
            while (resultSet.next()) {
                Object[] rowData = new Object[columnCount];
                for (int i = 1; i <= columnCount; i++) {
                    rowData[i-1] = resultSet.getObject(i);
                }
                defaultTableModel.addRow(rowData);
            }

            table.setModel(defaultTableModel);

            // 设置表格条纹效果
            table.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
                @Override
                public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                    Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                    if (isSelected) {
                        c.setBackground(new Color(232, 242, 254));
                        c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                    } else {
                        if (row % 2 == 0) {
                            c.setBackground(Color.WHITE);
                        } else {
                            c.setBackground(new Color(245, 247, 250));
                        }
                        c.setForeground(UIStyleUtil.TEXT_COLOR);
                    }

                    ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                    // 业主ID和业主姓名列居中显示，其他列左对齐
                    ((JLabel) c).setHorizontalAlignment(column == 0 || column == 1 ? JLabel.CENTER : JLabel.LEFT);

                    return c;
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除业主信息
     */
    private void deleteOwnerInfo() {
        // 检查是否选中了表格行
        int selectedRow = table.getSelectedRow();
        if (selectedRow == -1) {
            DialogUtil.showWarning(this, "请先选择要删除的业主信息");
            return;
        }

        // 获取选中的业主ID（第一列，索引为0）和姓名（第二列，索引为1）
        int ownerId = Integer.parseInt(table.getValueAt(selectedRow, 0).toString());
        String ownerName = table.getValueAt(selectedRow, 1).toString();

        // 显示确认对话框
        boolean confirmed = DialogUtil.showConfirm(this, "确定要删除业主 '"+ownerName+"' (ID: "+ownerId+") 的信息吗？");
        if (!confirmed) {
            return;
        }

        DatabaseConnection dbConnection = new DatabaseConnection();
        Owner owner = new Owner();
        OwnerDao ownerDao = new OwnerDao();

        // 设置删除条件
        owner.setId(ownerId);
        Connection connection = null;

        try {
            connection = dbConnection.getConnection();
            boolean success = ownerDao.delete(connection, owner);

            if (success) {
                DialogUtil.showSuccess(this, "删除业主信息成功");
                // 刷新表格
                queryOwnerInfo();
            } else {
                DialogUtil.showError(this, "删除业主信息失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            DialogUtil.showError(this, "删除失败: " + e.getMessage());
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
