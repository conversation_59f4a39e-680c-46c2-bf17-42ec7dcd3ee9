package com.property.model;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 账单模型类
 * 对应数据库中的t_payment_bill表
 */
public class PaymentBill {
    private int id;                 // 主键ID
    private String billNo;          // 账单编号
    private int ownerId;            // 业主ID
    private int feeTypeId;          // 费用类型ID
    private String billingPeriod;   // 账单周期（如：2023年1月）
    private BigDecimal amount;      // 应缴金额
    private BigDecimal paidAmount;  // 已缴金额
    private String status;          // 状态：未缴费、部分缴费、已缴费
    private Date dueDate;           // 缴费截止日期
    private String remark;          // 备注
    private Timestamp createTime;   // 创建时间
    private Timestamp updateTime;   // 更新时间

    // 非数据库字段，用于显示
    private String ownerName;       // 业主姓名
    private String feeTypeName;     // 费用类型名称

    // 构造函数
    public PaymentBill() {
    }

    public PaymentBill(int id, String billNo, int ownerId, int feeTypeId, String billingPeriod,
                      BigDecimal amount, BigDecimal paidAmount, String status, Date dueDate,
                      String remark) {
        this.id = id;
        this.billNo = billNo;
        this.ownerId = ownerId;
        this.feeTypeId = feeTypeId;
        this.billingPeriod = billingPeriod;
        this.amount = amount;
        this.paidAmount = paidAmount;
        this.status = status;
        this.dueDate = dueDate;
        this.remark = remark;
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public int getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(int ownerId) {
        this.ownerId = ownerId;
    }

    public int getFeeTypeId() {
        return feeTypeId;
    }

    public void setFeeTypeId(int feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public String getBillingPeriod() {
        return billingPeriod;
    }

    public void setBillingPeriod(String billingPeriod) {
        this.billingPeriod = billingPeriod;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    // 滞纳金字段已移除

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    @Override
    public String toString() {
        return "PaymentBill{" +
                "id=" + id +
                ", billNo='" + billNo + '\'' +
                ", ownerId=" + ownerId +
                ", feeTypeId=" + feeTypeId +
                ", billingPeriod='" + billingPeriod + '\'' +
                ", amount=" + amount +
                ", paidAmount=" + paidAmount +
                ", status='" + status + '\'' +
                ", dueDate=" + dueDate +
                ", remark='" + remark + '\'' +
                ", ownerName='" + ownerName + '\'' +
                ", feeTypeName='" + feeTypeName + '\'' +
                '}';
    }
}
