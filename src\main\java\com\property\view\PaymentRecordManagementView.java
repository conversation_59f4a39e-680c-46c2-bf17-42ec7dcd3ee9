package com.property.view;

import com.property.model.PaymentRecord;
import com.property.service.PaymentService;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 缴费记录管理界面
 */
public class PaymentRecordManagementView extends JFrame {
    private PaymentService paymentService;
    private JTable recordTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> paymentMethodComboBox;
    private JButton searchButton;
    private JButton viewButton;
    private JButton printButton;
    private JButton refreshButton;

    public PaymentRecordManagementView() {
        paymentService = new PaymentService();
        initComponents();
        loadRecords();
    }

    private void initComponents() {
        setTitle("缴费记录管理");
        setSize(1100, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);

        // 创建标题栏
        JPanel titleBar = new JPanel();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setPreferredSize(new Dimension(1000, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));

        // 添加系统标题
        JLabel titleLabel = new JLabel("缴费记录管理");
        titleLabel.setFont(UIStyleUtil.MENU_FONT);
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 0));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建表格模型
        String[] columnNames = {"ID", "记录编号", "账单编号", "业主姓名", "缴费金额", "缴费方式", "缴费时间", "操作员", "收据编号"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 设置表格不可编辑
            }
        };
        recordTable = new JTable(tableModel);
        recordTable.getTableHeader().setReorderingAllowed(false);
        recordTable.getTableHeader().setResizingAllowed(true);
        recordTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        recordTable.setRowHeight(25);

        // 设置表格列宽
        recordTable.getColumnModel().getColumn(0).setPreferredWidth(50);
        recordTable.getColumnModel().getColumn(1).setPreferredWidth(120);
        recordTable.getColumnModel().getColumn(2).setPreferredWidth(120);
        recordTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        recordTable.getColumnModel().getColumn(4).setPreferredWidth(100);
        recordTable.getColumnModel().getColumn(5).setPreferredWidth(100);
        recordTable.getColumnModel().getColumn(6).setPreferredWidth(150);
        recordTable.getColumnModel().getColumn(7).setPreferredWidth(100);
        recordTable.getColumnModel().getColumn(8).setPreferredWidth(120);

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(recordTable);

        // 创建搜索面板
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setOpaque(false);
        searchField = new JTextField(15);
        UIStyleUtil.styleTextField(searchField);

        String[] paymentMethodOptions = {"全部", "现金", "银行转账", "在线支付", "支付宝", "微信支付"};
        paymentMethodComboBox = new JComboBox<>(paymentMethodOptions);
        paymentMethodComboBox.setFont(UIStyleUtil.LABEL_FONT);

        searchButton = new UIStyleUtil.RoundedButton("搜索", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        JLabel nameLabel = new JLabel("业主姓名:");
        nameLabel.setFont(UIStyleUtil.LABEL_FONT);
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        JLabel methodLabel = new JLabel("缴费方式:");
        methodLabel.setFont(UIStyleUtil.LABEL_FONT);
        methodLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        searchPanel.add(nameLabel);
        searchPanel.add(searchField);
        searchPanel.add(methodLabel);
        searchPanel.add(paymentMethodComboBox);
        searchPanel.add(searchButton);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        viewButton = new UIStyleUtil.RoundedButton("查看详情", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        printButton = new UIStyleUtil.RoundedButton("打印收据", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        refreshButton = new UIStyleUtil.RoundedButton("刷新", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        buttonPanel.add(viewButton);
        buttonPanel.add(printButton);
        buttonPanel.add(refreshButton);

        // 创建顶部面板
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setOpaque(false);
        topPanel.add(searchPanel, BorderLayout.WEST);
        topPanel.add(buttonPanel, BorderLayout.EAST);

        // 创建内容面板
        JPanel mainPanel = new UIStyleUtil.ShadowPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 设置表格样式
        recordTable.getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 14));
        recordTable.getTableHeader().setBackground(UIStyleUtil.PRIMARY_COLOR);
        recordTable.getTableHeader().setForeground(Color.WHITE);

        // 设置表格条纹效果
        recordTable.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (isSelected) {
                    c.setBackground(new Color(232, 242, 254));
                    c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                } else {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 247, 250));
                    }
                    c.setForeground(UIStyleUtil.TEXT_COLOR);
                }

                ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                ((JLabel) c).setHorizontalAlignment(column == 0 ? JLabel.CENTER : JLabel.LEFT);

                return c;
            }
        });

        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // 创建北部面板，包含标题栏和顶部面板
        JPanel northPanel = new JPanel(new BorderLayout());
        northPanel.setOpaque(false);
        northPanel.add(titleBar, BorderLayout.NORTH);
        northPanel.add(topPanel, BorderLayout.SOUTH);

        // 设置布局
        setLayout(new BorderLayout());
        add(northPanel, BorderLayout.NORTH);
        add(mainPanel, BorderLayout.CENTER);

        // 添加事件监听器
        viewButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = recordTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) recordTable.getValueAt(selectedRow, 0);
                    showRecordDetailDialog(id);
                } else {
                    DialogUtil.showWarningDialog(PaymentRecordManagementView.this, "请先选择一条缴费记录");
                }
            }
        });

        printButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = recordTable.getSelectedRow();
                if (selectedRow >= 0) {
                    String receiptNo = (String) recordTable.getValueAt(selectedRow, 8);
                    printReceipt(receiptNo);
                } else {
                    DialogUtil.showWarningDialog(PaymentRecordManagementView.this, "请先选择一条缴费记录");
                }
            }
        });

        refreshButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                loadRecords();
            }
        });

        searchButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                searchRecords();
            }
        });
    }

    /**
     * 加载所有缴费记录
     */
    private void loadRecords() {
        // 清空表格
        tableModel.setRowCount(0);

        // 获取所有缴费记录
        List<PaymentRecord> records = paymentService.getAllRecords();

        // 填充表格
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (PaymentRecord record : records) {
            Object[] rowData = {
                record.getId(),
                record.getRecordNo(),
                record.getBillNo(),
                record.getOwnerName(),
                record.getPaymentAmount(),
                record.getPaymentMethod(),
                dateFormat.format(record.getPaymentTime()),
                record.getOperator(),
                record.getReceiptNo()
            };
            tableModel.addRow(rowData);
        }
    }

    /**
     * 搜索缴费记录
     */
    private void searchRecords() {
        String searchText = searchField.getText().trim();
        String paymentMethod = (String) paymentMethodComboBox.getSelectedItem();

        // 清空表格
        tableModel.setRowCount(0);

        // 获取所有缴费记录
        List<PaymentRecord> records = paymentService.getAllRecords();

        // 筛选并填充表格
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (PaymentRecord record : records) {
            boolean matchName = searchText.isEmpty() || record.getOwnerName().contains(searchText);
            boolean matchMethod = "全部".equals(paymentMethod) || record.getPaymentMethod().equals(paymentMethod);

            if (matchName && matchMethod) {
                Object[] rowData = {
                    record.getId(),
                    record.getRecordNo(),
                    record.getBillNo(),
                    record.getOwnerName(),
                    record.getPaymentAmount(),
                    record.getPaymentMethod(),
                    dateFormat.format(record.getPaymentTime()),
                    record.getOperator(),
                    record.getReceiptNo()
                };
                tableModel.addRow(rowData);
            }
        }
    }

    /**
     * 显示缴费记录详情对话框
     * @param id 缴费记录ID
     */
    private void showRecordDetailDialog(int id) {
        PaymentRecord record = paymentService.getRecordById(id);
        if (record == null) {
            DialogUtil.showErrorDialog(this, "获取缴费记录信息失败");
            return;
        }

        JDialog dialog = new JDialog(this, "缴费记录详情", true);
        dialog.setSize(500, 400);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        // 设置对话框背景
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        dialog.setContentPane(contentPane);

        // 缴费记录信息面板
        JPanel recordInfoPanel = new UIStyleUtil.ShadowPanel();
        recordInfoPanel.setLayout(new GridLayout(9, 2, 10, 10));
        recordInfoPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(UIStyleUtil.PRIMARY_COLOR, 1, true),
            "缴费记录信息",
            TitledBorder.DEFAULT_JUSTIFICATION,
            TitledBorder.DEFAULT_POSITION,
            UIStyleUtil.LABEL_FONT,
            UIStyleUtil.PRIMARY_COLOR));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 创建标签和值
        JLabel[] labels = new JLabel[9];
        JLabel[] values = new JLabel[9];

        String[] labelTexts = {
            "记录编号:", "账单编号:", "业主姓名:", "缴费金额:", "缴费方式:",
            "缴费时间:", "操作员:", "收据编号:", "备注:"
        };

        String[] valueTexts = {
            record.getRecordNo(),
            record.getBillNo(),
            record.getOwnerName(),
            record.getPaymentAmount().toString(),
            record.getPaymentMethod(),
            dateFormat.format(record.getPaymentTime()),
            record.getOperator(),
            record.getReceiptNo(),
            record.getRemark() != null ? record.getRemark() : ""
        };

        // 设置标签和值的样式
        for (int i = 0; i < 9; i++) {
            labels[i] = new JLabel(labelTexts[i]);
            labels[i].setFont(UIStyleUtil.LABEL_FONT);
            labels[i].setForeground(UIStyleUtil.LABEL_COLOR);
            labels[i].setHorizontalAlignment(JLabel.RIGHT);

            values[i] = new JLabel(valueTexts[i]);
            values[i].setFont(UIStyleUtil.LABEL_FONT);
            values[i].setForeground(UIStyleUtil.TEXT_COLOR);
            values[i].setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 0));

            recordInfoPanel.add(labels[i]);
            recordInfoPanel.add(values[i]);
        }

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        JButton printButton = new UIStyleUtil.RoundedButton("打印收据", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        JButton closeButton = new UIStyleUtil.RoundedButton("关闭", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        buttonPanel.add(printButton);
        buttonPanel.add(closeButton);

        dialog.add(recordInfoPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        printButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                printReceipt(record.getReceiptNo());
            }
        });

        closeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 打印收据
     * @param receiptNo 收据编号
     */
    private void printReceipt(String receiptNo) {
        // 这里简化处理，实际应该调用打印API
        DialogUtil.showInfoDialog(this, "正在打印收据: " + receiptNo);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                new PaymentRecordManagementView().setVisible(true);
            }
        });
    }
}
