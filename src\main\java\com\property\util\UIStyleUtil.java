package com.property.util;

import java.awt.Color;
import java.awt.Component;
import java.awt.Cursor;
import java.awt.Font;
import java.awt.GradientPaint;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.geom.RoundRectangle2D;

import javax.swing.BorderFactory;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JComponent;
import javax.swing.JMenu;
import javax.swing.JMenuItem;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.border.AbstractBorder;
import javax.swing.border.Border;

/**
 * UI样式工具类 - 提供统一的现代化界面风格
 */
public class UIStyleUtil {
    // 颜色方案
    public static final Color PRIMARY_COLOR = new Color(25, 42, 86); // 深蓝色
    public static final Color SECONDARY_COLOR = new Color(45, 95, 160); // 中蓝色
    public static final Color ACCENT_COLOR = new Color(0, 150, 136); // 青绿色
    public static final Color BACKGROUND_COLOR_START = new Color(245, 247, 250); // 浅灰蓝色
    public static final Color BACKGROUND_COLOR_END = new Color(255, 255, 255); // 白色
    public static final Color TEXT_COLOR = new Color(33, 33, 33); // 深灰色
    public static final Color LABEL_COLOR = new Color(90, 90, 90); // 标签文字颜色
    public static final Color TITLE_COLOR = new Color(255, 255, 255); // 标题文字颜色
    public static final Color FIELD_BACKGROUND = new Color(250, 250, 250); // 输入框背景色
    public static final Color MENU_HOVER_COLOR = new Color(35, 52, 96); // 菜单悬停颜色
    public static final Color LOGIN_BUTTON_COLOR = new Color(19, 35, 73); // 登录按钮深蓝色
    public static final Color REGISTER_BUTTON_COLOR = new Color(41, 128, 185); // 注册按钮蓝色

    // 字体
    public static final Font TITLE_FONT = new Font("微软雅黑", Font.BOLD, 38);
    public static final Font LABEL_FONT = new Font("微软雅黑", Font.BOLD, 16);
    public static final Font INPUT_FONT = new Font("微软雅黑", Font.PLAIN, 16);
    public static final Font BUTTON_FONT = new Font("微软雅黑", Font.BOLD, 16);
    public static final Font MENU_FONT = new Font("微软雅黑", Font.BOLD, 20);
    public static final Font COPYRIGHT_FONT = new Font("微软雅黑", Font.PLAIN, 12);

    /**
     * 渐变背景面板
     */
    public static class GradientPanel extends JPanel {
        private static final long serialVersionUID = 1L;

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g;
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int w = getWidth();
            int h = getHeight();

            // 绘制渐变背景
            GradientPaint gp = new GradientPaint(0, 0, BACKGROUND_COLOR_START, 0, h, BACKGROUND_COLOR_END);
            g2d.setPaint(gp);
            g2d.fillRect(0, 0, w, h);
        }
    }

    /**
     * 带阴影的面板
     */
    public static class ShadowPanel extends JPanel {
        private static final long serialVersionUID = 1L;
        private int shadowSize = 5;

        public ShadowPanel() {
            setOpaque(false);
        }

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g;
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 绘制阴影
            for (int i = 0; i < shadowSize; i++) {
                g2d.setColor(new Color(0, 0, 0, 20 - i * 4));
                g2d.drawRoundRect(i, i, getWidth() - i * 2 - 1, getHeight() - i * 2 - 1, 15, 15);
            }

            // 绘制面板背景
            g2d.setColor(Color.WHITE);
            g2d.fillRoundRect(shadowSize, shadowSize, getWidth() - shadowSize * 2, getHeight() - shadowSize * 2, 10, 10);
        }
    }

    /**
     * 圆角按钮
     */
    public static class RoundedButton extends JButton {
        private static final long serialVersionUID = 1L;
        private Color backgroundColor;
        private Color hoverColor;
        private boolean isHovered = false;

        public RoundedButton(String text, Color bgColor, Color hoverColor) {
            super(text);
            this.backgroundColor = bgColor;
            this.hoverColor = hoverColor;
            setContentAreaFilled(false);
            setFocusPainted(false);
            setBorderPainted(false);
            setOpaque(false);
            setForeground(Color.WHITE);
            setFont(BUTTON_FONT);

            addMouseListener(new MouseAdapter() {
                @Override
                public void mouseEntered(MouseEvent e) {
                    isHovered = true;
                    setCursor(new Cursor(Cursor.HAND_CURSOR));
                    repaint();
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    isHovered = false;
                    setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
                    repaint();
                }
            });
        }

        @Override
        protected void paintComponent(Graphics g) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 绘制按钮阴影
            g2.setColor(new Color(0, 0, 0, 30));
            g2.fill(new RoundRectangle2D.Float(2, 2, getWidth() - 4, getHeight() - 4, 15, 15));

            if (isHovered) {
                g2.setColor(hoverColor);
            } else {
                g2.setColor(backgroundColor);
            }

            g2.fill(new RoundRectangle2D.Float(0, 0, getWidth() - 2, getHeight() - 2, 15, 15));
            super.paintComponent(g);
            g2.dispose();
        }
    }

    /**
     * 自定义文本输入框边框
     */
    public static class RoundedBorder extends AbstractBorder {
        private static final long serialVersionUID = 1L;
        private Color borderColor;
        private int radius;

        public RoundedBorder(Color color, int radius) {
            this.borderColor = color;
            this.radius = radius;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2.setColor(borderColor);
            g2.drawRoundRect(x, y, width - 1, height - 1, radius, radius);
            g2.dispose();
        }
    }

    /**
     * 为文本输入框添加样式和焦点效果
     */
    public static void styleTextField(JTextField textField) {
        textField.setFont(INPUT_FONT);
        textField.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(SECONDARY_COLOR, 10),
                BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        textField.setBackground(FIELD_BACKGROUND);
        textField.setForeground(TEXT_COLOR);

        // 添加焦点监听器以实现输入框高亮效果
        textField.addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                textField.setBorder(BorderFactory.createCompoundBorder(
                        new RoundedBorder(ACCENT_COLOR, 10),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
                textField.setBackground(Color.WHITE);
            }

            @Override
            public void focusLost(FocusEvent e) {
                textField.setBorder(BorderFactory.createCompoundBorder(
                        new RoundedBorder(SECONDARY_COLOR, 10),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
                textField.setBackground(FIELD_BACKGROUND);
            }
        });
    }

    /**
     * 获取应用图标
     */
    public static ImageIcon getApplicationIcon() {
        try {
            return new ImageIcon(UIStyleUtil.class.getResource("/imagine/图标.png"));
        } catch (Exception e) {
            // 如果无法加载图标，返回空
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建圆角边框
     */
    public static Border createRoundedBorder(Color color, int radius, int padding) {
        return BorderFactory.createCompoundBorder(
                new RoundedBorder(color, radius),
                BorderFactory.createEmptyBorder(padding, padding, padding, padding));
    }

    /**
     * 自定义弹出菜单样式
     */
    public static class ModernPopupMenuUI extends javax.swing.plaf.basic.BasicPopupMenuUI {
        @Override
        public void paint(Graphics g, JComponent c) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 绘制圆角矩形背景
            g2d.setColor(Color.WHITE);
            g2d.fill(new RoundRectangle2D.Float(0, 0, c.getWidth(), c.getHeight(), 10, 10));

            // 绘制边框
            g2d.setColor(new Color(230, 230, 230));
            g2d.draw(new RoundRectangle2D.Float(0, 0, c.getWidth() - 1, c.getHeight() - 1, 10, 10));

            // 绘制阴影
            for (int i = 0; i < 5; i++) {
                g2d.setColor(new Color(0, 0, 0, 10 - i * 2));
                g2d.draw(new RoundRectangle2D.Float(i, i, c.getWidth() - i * 2 - 1, c.getHeight() - i * 2 - 1, 10, 10));
            }

            g2d.dispose();
            super.paint(g, c);
        }
    }

    /**
     * 自定义菜单项渲染器
     */
    public static class ModernMenuItemRenderer extends javax.swing.plaf.basic.BasicMenuItemUI {
        @Override
        protected void paintBackground(Graphics g, JMenuItem menuItem, Color bgColor) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            if (menuItem.isArmed() || (menuItem instanceof JMenu && menuItem.isSelected())) {
                // 选中或悬停时的背景
                g2d.setColor(new Color(240, 240, 240));
                g2d.fillRoundRect(2, 2, menuItem.getWidth() - 4, menuItem.getHeight() - 4, 8, 8);
            }

            g2d.dispose();
        }
    }
}
