@echo off
echo 正在启动物业管理系统...
echo.

rem 检查是否在dist目录中运行
if exist PropertyManagementSystem.jar (
    rem 在dist目录中运行
    set CLASSPATH=.;lib\*;jdbc\*
) else (
    rem 在项目根目录中运行
    set CLASSPATH=.;target\classes;lib\*;jdbc\*
)

rem 设置Java路径
set JAVA_HOME=java

rem 检查是否存在JAR文件
if exist PropertyManagementSystem.jar (
    echo 使用JAR文件启动...
    %JAVA_HOME% -jar PropertyManagementSystem.jar
) else (
    echo 使用类文件启动...
    %JAVA_HOME% -cp %CLASSPATH% com.property.app
)

echo.
echo 程序已退出
pause
