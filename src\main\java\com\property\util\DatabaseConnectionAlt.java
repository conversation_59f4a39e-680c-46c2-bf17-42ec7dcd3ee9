package com.property.util;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * 备用数据库连接工具类
 */
public class DatabaseConnectionAlt {

    /**
     * 获取数据库连接
     * @return 数据库连接对象
     * @throws Exception 如果获取连接时发生异常
     */
    public Connection getConnection() throws Exception {
        Class.forName(DatabaseConfig.getDriverName());
        Connection connection = DriverManager.getConnection(
            DatabaseConfig.getJdbcUrl(),
            DatabaseConfig.getUsername(),
            DatabaseConfig.getPassword()
        );
        return connection;
    }

    /**
     * 关闭数据库连接
     * @param connection 要关闭的连接对象
     * @throws Exception 如果关闭连接时发生异常
     */
    public void closeConnection(Connection connection) throws Exception {
        if (connection != null) {
            connection.close();
        }
    }

    /**
     * 测试数据库连接
     */
    public static void main(String[] args) throws CloneNotSupportedException {
        DatabaseConnectionAlt dbConnection = new DatabaseConnectionAlt();
        try {
            dbConnection.getConnection();
            System.out.println("数据库连接成功");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("数据库连接失败");
        }
    }

    // 为了兼容旧代码，保留以下方法
    public Connection getCon() throws Exception {
        return getConnection();
    }

    public void closeCon(Connection con) throws Exception {
        closeConnection(con);
    }
}
