package com.property.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.EventQueue;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.ResultSet;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuBar;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;

import com.property.dao.OwnerDao;
import com.property.model.Owner;
import com.property.util.DatabaseConnection;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;
import com.property.util.UIStyleUtil.GradientPanel;
import com.property.util.UIStyleUtil.RoundedButton;
import com.property.util.UIStyleUtil.ShadowPanel;

/**
 * 报修请求查看界面
 */
public class RepairRequestView extends JFrame {

    private static final long serialVersionUID = 1L;
    private GradientPanel contentPane;
    private JTable table;
    private JLabel nameLabel;
    private JTextField nameField;
    private RoundedButton queryButton;
    private RoundedButton deleteButton;

    /**
     * 启动应用程序
     */
    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            public void run() {
                try {
                    RepairRequestView frame = new RepairRequestView();
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 创建报修请求查看界面
     */
    public RepairRequestView() {
        setTitle("报修请求");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setBounds(100, 100, 800, 600);
        setLocationRelativeTo(null); // 居中显示

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        contentPane = new GradientPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);
        contentPane.setLayout(new BorderLayout());

        // 创建现代化菜单栏作为标题栏
        JMenuBar titleBar = new JMenuBar();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setBorder(BorderFactory.createEmptyBorder());
        titleBar.setPreferredSize(new Dimension(800, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));
        contentPane.add(titleBar, BorderLayout.NORTH);

        // 添加系统标题
        JLabel titleLabel = new JLabel("报修请求");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 40));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建内容面板
        ShadowPanel contentPanel = new ShadowPanel();
        contentPanel.setLayout(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        contentPane.add(contentPanel, BorderLayout.CENTER);

        // 创建搜索面板
        JPanel searchPanel = new JPanel();
        searchPanel.setOpaque(false);
        searchPanel.setLayout(new BoxLayout(searchPanel, BoxLayout.X_AXIS));
        searchPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 20, 10));
        contentPanel.add(searchPanel, BorderLayout.NORTH);

        // 添加弹性空间
        searchPanel.add(Box.createHorizontalGlue());

        // 姓名标签
        nameLabel = new JLabel("姓名：");
        nameLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);
        searchPanel.add(nameLabel);

        // 添加间距
        searchPanel.add(Box.createRigidArea(new Dimension(10, 0)));

        // 姓名输入框
        nameField = new JTextField();
        nameField.setPreferredSize(new Dimension(150, 30));
        nameField.setMaximumSize(new Dimension(150, 30));
        UIStyleUtil.styleTextField(nameField);
        searchPanel.add(nameField);

        // 添加间距
        searchPanel.add(Box.createRigidArea(new Dimension(20, 0)));

        // 查询按钮
        queryButton = new RoundedButton("查询", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        queryButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                queryRepairRequests();
            }
        });
        searchPanel.add(queryButton);

        // 添加间距
        searchPanel.add(Box.createRigidArea(new Dimension(10, 0)));

        // 删除按钮
        deleteButton = new RoundedButton("删除", new Color(220, 53, 69), new Color(200, 35, 51));
        deleteButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                deleteRepairRequest();
            }
        });
        searchPanel.add(deleteButton);

        // 添加弹性空间
        searchPanel.add(Box.createHorizontalGlue());

        // 创建表格面板
        JScrollPane scrollPane = new JScrollPane();
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        scrollPane.getViewport().setBackground(Color.WHITE);
        contentPanel.add(scrollPane, BorderLayout.CENTER);

        // 表格设置
        table = new JTable();
        table.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        table.setRowHeight(30);
        table.setShowGrid(false);
        table.setIntercellSpacing(new Dimension(0, 0));
        table.setFillsViewportHeight(true);

        // 设置表格模型
        table.setModel(new DefaultTableModel(
            new Object[][] {},
            new String[] {}
        ) {
            private static final long serialVersionUID = 1L;
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 表格不可编辑
            }
        });

        // 设置表头样式
        JTableHeader header = table.getTableHeader();
        header.setFont(new Font("微软雅黑", Font.BOLD, 14));
        header.setBackground(UIStyleUtil.PRIMARY_COLOR);
        header.setForeground(Color.WHITE);
        header.setPreferredSize(new Dimension(header.getWidth(), 35));

        // 设置表头居中显示
        ((javax.swing.table.DefaultTableCellRenderer)header.getDefaultRenderer()).setHorizontalAlignment(javax.swing.SwingConstants.CENTER);

        // 设置表格选择模式
        table.setSelectionBackground(new Color(232, 242, 254));
        table.setSelectionForeground(UIStyleUtil.TEXT_COLOR);

        scrollPane.setViewportView(table);

        // 初始化时加载所有数据
        nameField.setText(""); // 确保姓名字段为空，查询所有数据
        queryRepairRequests();
    }

    /**
     * 查询报修请求
     */
    private void queryRepairRequests() {
        DatabaseConnection dbConnection = new DatabaseConnection();
        Owner owner = new Owner();
        OwnerDao ownerDao = new OwnerDao();
        ResultSet resultSet = null;

        // 设置查询条件
        owner.setName(nameField.getText().trim());
        Connection connection = null;
        DefaultTableModel defaultTableModel = new DefaultTableModel() {
            private static final long serialVersionUID = 1L;
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 表格不可编辑
            }
        };

        try {
            connection = dbConnection.getConnection();
            resultSet = ownerDao.queryRepairRequestByName(connection, owner);

            // 获取列数
            int columnCount = resultSet.getMetaData().getColumnCount();

            // 创建中文列名数组
            String[] chineseColumnNames = {"编号", "业主姓名", "门牌号", "报修内容", "处理状态", "创建时间", "更新时间"};

            // 添加列名（使用中文列名）
            for (int i = 0; i < chineseColumnNames.length && i < columnCount; i++) {
                defaultTableModel.addColumn(chineseColumnNames[i]);
            }

            table.setModel(defaultTableModel);

            // 添加数据行
            while (resultSet.next()) {
                Object[] rowData = new Object[columnCount];
                for (int i = 1; i <= columnCount; i++) {
                    rowData[i-1] = resultSet.getObject(i);
                }
                defaultTableModel.addRow(rowData);
            }

            table.setModel(defaultTableModel);

            // 设置表格列宽
            if (table.getColumnCount() >= 7) {
                // 编号列
                table.getColumnModel().getColumn(0).setPreferredWidth(50);
                // 业主姓名列
                table.getColumnModel().getColumn(1).setPreferredWidth(80);
                // 门牌号列
                table.getColumnModel().getColumn(2).setPreferredWidth(80);
                // 报修内容列
                table.getColumnModel().getColumn(3).setPreferredWidth(200);
                // 处理状态列
                table.getColumnModel().getColumn(4).setPreferredWidth(80);
                // 创建时间列
                table.getColumnModel().getColumn(5).setPreferredWidth(150);
                // 更新时间列
                table.getColumnModel().getColumn(6).setPreferredWidth(150);
            }

            // 设置表格条纹效果
            // 创建自定义渲染器
            javax.swing.table.DefaultTableCellRenderer centerRenderer = new javax.swing.table.DefaultTableCellRenderer() {
                private static final long serialVersionUID = 1L;
                @Override
                public java.awt.Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                    java.awt.Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                    if (!isSelected) {
                        c.setBackground(row % 2 == 0 ? new Color(245, 247, 250) : Color.WHITE);
                    }
                    return c;
                }
            };

            // 设置内容居中显示
            centerRenderer.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);

            // 应用渲染器到所有列
            for (int i = 0; i < table.getColumnCount(); i++) {
                table.getColumnModel().getColumn(i).setCellRenderer(centerRenderer);
            }

            // 内容列左对齐
            if (table.getColumnCount() > 3) {
                javax.swing.table.DefaultTableCellRenderer leftRenderer = new javax.swing.table.DefaultTableCellRenderer() {
                    private static final long serialVersionUID = 1L;
                    @Override
                    public java.awt.Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                        java.awt.Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                        if (!isSelected) {
                            c.setBackground(row % 2 == 0 ? new Color(245, 247, 250) : Color.WHITE);
                        }
                        return c;
                    }
                };
                leftRenderer.setHorizontalAlignment(javax.swing.SwingConstants.LEFT);
                table.getColumnModel().getColumn(3).setCellRenderer(leftRenderer);
            }
        } catch (Exception e) {
            e.printStackTrace();
            DialogUtil.showError(this, "查询失败: " + e.getMessage());
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除报修请求
     */
    private void deleteRepairRequest() {
        // 验证是否输入了姓名
        String name = nameField.getText().trim();
        if (name.isEmpty()) {
            DialogUtil.showWarning(this, "请输入要删除的业主姓名");
            return;
        }

        // 显示确认对话框
        int result = DialogUtil.showConfirmDialog(this, "确定要删除 " + name + " 的所有报修请求吗？", "删除确认",
                DialogUtil.YES_NO_OPTION, DialogUtil.QUESTION_MESSAGE);

        if (result != DialogUtil.YES_OPTION) {
            return;
        }

        DatabaseConnection dbConnection = new DatabaseConnection();
        Owner owner = new Owner();
        OwnerDao ownerDao = new OwnerDao();

        // 设置删除条件
        owner.setName(name);
        Connection connection = null;

        try {
            connection = dbConnection.getConnection();
            ownerDao.deleteRepairRequest(connection, owner);

            // 显示成功提示
            DialogUtil.showSuccess(this, "删除成功！");

            // 清空输入框
            nameField.setText("");

            // 刷新表格
            queryRepairRequests();
        } catch (Exception e) {
            e.printStackTrace();
            DialogUtil.showError(this, "删除失败: " + e.getMessage());
        } finally {
            try {
                if (connection != null) {
                    dbConnection.closeConnection(connection);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
