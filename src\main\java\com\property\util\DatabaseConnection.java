package com.property.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接工具类
 */
public class DatabaseConnection {
    /**
     * 关闭数据库连接
     * @param connection 要关闭的连接对象
     * @throws SQLException 如果关闭连接时发生SQL异常
     */
    public void closeConnection(Connection connection) throws SQLException {
        if (connection != null) {
            connection.close();
        }
    }

    /**
     * 获取数据库连接
     * @return 数据库连接对象
     * @throws SQLException 如果获取连接时发生SQL异常
     * @throws ClassNotFoundException 如果找不到JDBC驱动类
     */
    public Connection getConnection() throws SQLException, ClassNotFoundException {
        Class.forName(DatabaseConfig.getDriverName());
        Connection connection = DriverManager.getConnection(
            DatabaseConfig.getJdbcUrl(),
            DatabaseConfig.getUsername(),
            DatabaseConfig.getPassword()
        );
        return connection;
    }

    /**
     * 测试数据库连接
     */
    public static void main(String[] args) throws SQLException, CloneNotSupportedException {
        DatabaseConnection dbConnection = new DatabaseConnection();
        try {
            dbConnection.getConnection();
            System.out.println("连接数据库成功！");
        } catch (Exception e) {
            System.out.println("连接数据库失败！");
            e.printStackTrace();
        }
    }

    // 为了兼容旧代码，保留以下方法
    public Connection getCon() throws SQLException, ClassNotFoundException {
        return getConnection();
    }

    public void closeCon(Connection con) throws SQLException {
        closeConnection(con);
    }
}
