package com.property.view;

import com.property.model.FeeType;
import com.property.model.PaymentBill;
import com.property.service.PaymentService;
import com.property.util.DialogUtil;
import com.property.util.UIStyleUtil;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 账单管理界面
 */
public class PaymentBillManagementView extends JFrame {
    private PaymentService paymentService;
    private JTable billTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> statusComboBox;
    private JButton searchButton;
    private JButton generateButton;
    private JButton viewButton;
    private JButton paymentButton;
    private JButton deleteButton;
    private JButton refreshButton;

    public PaymentBillManagementView() {
        paymentService = new PaymentService();
        initComponents();
        loadBills();
    }

    private void initComponents() {
        setTitle("账单管理");
        setSize(1100, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置程序图标
        try {
            setIconImage(UIStyleUtil.getApplicationIcon().getImage());
        } catch (Exception e) {
            // 如果无法设置图标，忽略异常
            e.printStackTrace();
        }

        // 创建渐变背景面板
        JPanel contentPane = new UIStyleUtil.GradientPanel();
        contentPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        setContentPane(contentPane);

        // 创建标题栏
        JPanel titleBar = new JPanel();
        titleBar.setBackground(UIStyleUtil.PRIMARY_COLOR);
        titleBar.setPreferredSize(new Dimension(1000, 50));
        titleBar.setLayout(new BoxLayout(titleBar, BoxLayout.X_AXIS));

        // 添加系统标题
        JLabel titleLabel = new JLabel("账单管理");
        titleLabel.setFont(UIStyleUtil.MENU_FONT);
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 0));
        titleBar.add(titleLabel);

        // 添加弹性空间
        titleBar.add(Box.createHorizontalGlue());

        // 创建表格模型
        String[] columnNames = {"ID", "账单编号", "业主姓名", "费用类型", "账单周期", "应缴金额", "已缴金额", "状态", "截止日期"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 设置表格不可编辑
            }
        };
        billTable = new JTable(tableModel);
        billTable.getTableHeader().setReorderingAllowed(false);
        billTable.getTableHeader().setResizingAllowed(true);
        billTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        billTable.setRowHeight(25);

        // 设置表格列宽
        billTable.getColumnModel().getColumn(0).setPreferredWidth(50);
        billTable.getColumnModel().getColumn(1).setPreferredWidth(120);
        billTable.getColumnModel().getColumn(2).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(4).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(5).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(6).setPreferredWidth(100);
        billTable.getColumnModel().getColumn(7).setPreferredWidth(80);
        billTable.getColumnModel().getColumn(8).setPreferredWidth(100);

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(billTable);

        // 创建搜索面板
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setOpaque(false);
        searchField = new JTextField(15);
        UIStyleUtil.styleTextField(searchField);

        String[] statusOptions = {"全部", "未缴费", "部分缴费", "已缴费"};
        statusComboBox = new JComboBox<>(statusOptions);
        statusComboBox.setFont(UIStyleUtil.LABEL_FONT);

        searchButton = new UIStyleUtil.RoundedButton("搜索", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        JLabel nameLabel = new JLabel("业主姓名:");
        nameLabel.setFont(UIStyleUtil.LABEL_FONT);
        nameLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        JLabel statusLabel = new JLabel("状态:");
        statusLabel.setFont(UIStyleUtil.LABEL_FONT);
        statusLabel.setForeground(UIStyleUtil.LABEL_COLOR);

        searchPanel.add(nameLabel);
        searchPanel.add(searchField);
        searchPanel.add(statusLabel);
        searchPanel.add(statusComboBox);
        searchPanel.add(searchButton);

        // 创建按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setOpaque(false);

        generateButton = new UIStyleUtil.RoundedButton("生成账单", UIStyleUtil.ACCENT_COLOR, UIStyleUtil.SECONDARY_COLOR);
        viewButton = new UIStyleUtil.RoundedButton("查看详情", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        paymentButton = new UIStyleUtil.RoundedButton("缴费处理", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);
        deleteButton = new UIStyleUtil.RoundedButton("删除", new Color(231, 76, 60), UIStyleUtil.SECONDARY_COLOR);
        refreshButton = new UIStyleUtil.RoundedButton("刷新", UIStyleUtil.SECONDARY_COLOR, UIStyleUtil.PRIMARY_COLOR);

        buttonPanel.add(generateButton);
        buttonPanel.add(viewButton);
        buttonPanel.add(paymentButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(refreshButton);

        // 创建顶部面板
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setOpaque(false);
        topPanel.add(searchPanel, BorderLayout.WEST);
        topPanel.add(buttonPanel, BorderLayout.EAST);

        // 创建内容面板
        JPanel mainPanel = new UIStyleUtil.ShadowPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 设置表格样式
        billTable.getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 14));
        billTable.getTableHeader().setBackground(UIStyleUtil.PRIMARY_COLOR);
        billTable.getTableHeader().setForeground(Color.WHITE);

        // 设置表格条纹效果
        billTable.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (isSelected) {
                    c.setBackground(new Color(232, 242, 254));
                    c.setForeground(UIStyleUtil.PRIMARY_COLOR);
                } else {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 247, 250));
                    }
                    c.setForeground(UIStyleUtil.TEXT_COLOR);
                }

                ((JLabel) c).setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10));
                ((JLabel) c).setHorizontalAlignment(column == 0 ? JLabel.CENTER : JLabel.LEFT);

                return c;
            }
        });

        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // 设置布局
        setLayout(new BorderLayout());

        // 创建北部面板，包含标题栏和顶部面板
        JPanel northPanel = new JPanel(new BorderLayout());
        northPanel.setOpaque(false);
        northPanel.add(titleBar, BorderLayout.NORTH);
        northPanel.add(topPanel, BorderLayout.SOUTH);

        add(northPanel, BorderLayout.NORTH);
        add(mainPanel, BorderLayout.CENTER);

        // 添加事件监听器
        generateButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                showGenerateBillDialog();
            }
        });

        viewButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = billTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) billTable.getValueAt(selectedRow, 0);
                    showBillDetailDialog(id);
                } else {
                    DialogUtil.showWarningDialog(PaymentBillManagementView.this, "请先选择一个账单");
                }
            }
        });

        paymentButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = billTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) billTable.getValueAt(selectedRow, 0);
                    String status = (String) billTable.getValueAt(selectedRow, 7);
                    if ("已缴费".equals(status)) {
                        DialogUtil.showWarningDialog(PaymentBillManagementView.this, "该账单已缴清，无需再次缴费");
                    } else {
                        showPaymentDialog(id);
                    }
                } else {
                    DialogUtil.showWarningDialog(PaymentBillManagementView.this, "请先选择一个账单");
                }
            }
        });

        deleteButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = billTable.getSelectedRow();
                if (selectedRow >= 0) {
                    int id = (int) billTable.getValueAt(selectedRow, 0);
                    deleteBill(id);
                } else {
                    DialogUtil.showWarningDialog(PaymentBillManagementView.this, "请先选择一个账单");
                }
            }
        });

        refreshButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                loadBills();
            }
        });

        searchButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                searchBills();
            }
        });
    }

    /**
     * 加载所有账单
     */
    private void loadBills() {
        // 清空表格
        tableModel.setRowCount(0);

        // 获取所有账单
        List<PaymentBill> bills = paymentService.getAllBills();

        // 填充表格
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (PaymentBill bill : bills) {
            Object[] rowData = {
                bill.getId(),
                bill.getBillNo(),
                bill.getOwnerName(),
                bill.getFeeTypeName(),
                bill.getBillingPeriod(),
                bill.getAmount(),
                bill.getPaidAmount(),
                bill.getStatus(),
                dateFormat.format(bill.getDueDate())
            };
            tableModel.addRow(rowData);
        }
    }

    /**
     * 搜索账单
     */
    private void searchBills() {
        String searchText = searchField.getText().trim();
        String status = (String) statusComboBox.getSelectedItem();

        // 清空表格
        tableModel.setRowCount(0);

        // 获取账单
        List<PaymentBill> bills;
        if ("全部".equals(status)) {
            bills = paymentService.getAllBills();
        } else {
            bills = paymentService.getBillsByStatus(status);
        }

        // 筛选并填充表格
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (PaymentBill bill : bills) {
            if (searchText.isEmpty() || bill.getOwnerName().contains(searchText)) {
                Object[] rowData = {
                    bill.getId(),
                    bill.getBillNo(),
                    bill.getOwnerName(),
                    bill.getFeeTypeName(),
                    bill.getBillingPeriod(),
                    bill.getAmount(),
                    bill.getPaidAmount(),
                    bill.getStatus(),
                    dateFormat.format(bill.getDueDate())
                };
                tableModel.addRow(rowData);
            }
        }
    }

    /**
     * 显示生成账单对话框
     */
    private void showGenerateBillDialog() {
        JDialog dialog = new JDialog(this, "生成账单", true);
        dialog.setSize(500, 400);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        JPanel formPanel = new JPanel(new GridLayout(5, 2, 10, 10));
        formPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 获取所有启用的费用类型
        List<FeeType> feeTypes = paymentService.getActiveFeeTypes();
        String[] feeTypeNames = new String[feeTypes.size()];
        int[] feeTypeIds = new int[feeTypes.size()];
        for (int i = 0; i < feeTypes.size(); i++) {
            feeTypeNames[i] = feeTypes.get(i).getName();
            feeTypeIds[i] = feeTypes.get(i).getId();
        }

        JComboBox<String> feeTypeComboBox = new JComboBox<>(feeTypeNames);

        JTextField billingPeriodField = new JTextField(LocalDate.now().getYear() + "年" + LocalDate.now().getMonthValue() + "月");

        // 日期选择器
        JSpinner dueDateSpinner = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor dateEditor = new JSpinner.DateEditor(dueDateSpinner, "yyyy-MM-dd");
        dueDateSpinner.setEditor(dateEditor);
        dueDateSpinner.setValue(java.util.Date.from(LocalDate.now().plusMonths(1).atStartOfDay().toInstant(java.time.ZoneOffset.UTC)));

        JTextArea ownerListArea = new JTextArea();
        ownerListArea.setLineWrap(true);
        ownerListArea.setWrapStyleWord(true);
        JScrollPane ownerListScrollPane = new JScrollPane(ownerListArea);
        ownerListScrollPane.setPreferredSize(new Dimension(200, 100));

        JTextField remarkField = new JTextField();

        formPanel.add(new JLabel("费用类型:"));
        formPanel.add(feeTypeComboBox);
        formPanel.add(new JLabel("账单周期:"));
        formPanel.add(billingPeriodField);
        formPanel.add(new JLabel("截止日期:"));
        formPanel.add(dueDateSpinner);
        formPanel.add(new JLabel("业主ID列表:"));
        formPanel.add(ownerListScrollPane);
        formPanel.add(new JLabel("备注:"));
        formPanel.add(remarkField);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton generateButton = new JButton("生成");
        JButton cancelButton = new JButton("取消");
        buttonPanel.add(generateButton);
        buttonPanel.add(cancelButton);

        dialog.add(formPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        generateButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 验证输入
                String billingPeriod = billingPeriodField.getText().trim();
                String ownerListText = ownerListArea.getText().trim();
                String remark = remarkField.getText().trim();

                if (billingPeriod.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入账单周期");
                    return;
                }

                if (ownerListText.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入业主ID列表");
                    return;
                }

                // 解析业主ID列表
                List<Integer> ownerIds = new ArrayList<>();
                String[] ownerIdStrings = ownerListText.split("[,，\\s]+");
                for (String ownerIdString : ownerIdStrings) {
                    try {
                        int ownerId = Integer.parseInt(ownerIdString.trim());
                        ownerIds.add(ownerId);
                    } catch (NumberFormatException ex) {
                        DialogUtil.showWarningDialog(dialog, "业主ID必须是有效的数字: " + ownerIdString);
                        return;
                    }
                }

                // 获取选择的费用类型ID
                int selectedIndex = feeTypeComboBox.getSelectedIndex();
                if (selectedIndex < 0) {
                    DialogUtil.showWarningDialog(dialog, "请选择费用类型");
                    return;
                }
                int feeTypeId = feeTypeIds[selectedIndex];

                // 获取截止日期
                java.util.Date utilDate = (java.util.Date) dueDateSpinner.getValue();
                Date dueDate = new Date(utilDate.getTime());

                // 生成账单
                int successCount = paymentService.generateBills(feeTypeId, ownerIds, billingPeriod, dueDate, remark);

                if (successCount > 0) {
                    DialogUtil.showInfoDialog(dialog, "成功生成 " + successCount + " 个账单");
                    dialog.dispose();
                    loadBills();
                } else {
                    DialogUtil.showErrorDialog(dialog, "账单生成失败");
                }
            }
        });

        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 显示账单详情对话框
     * @param id 账单ID
     */
    private void showBillDetailDialog(int id) {
        PaymentBill bill = paymentService.getBillById(id);
        if (bill == null) {
            DialogUtil.showErrorDialog(this, "获取账单信息失败");
            return;
        }

        JDialog dialog = new JDialog(this, "账单详情", true);
        dialog.setSize(600, 500);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        // 账单信息面板
        JPanel billInfoPanel = new JPanel(new GridLayout(10, 2, 10, 10));
        billInfoPanel.setBorder(BorderFactory.createTitledBorder("账单信息"));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        billInfoPanel.add(new JLabel("账单编号:"));
        billInfoPanel.add(new JLabel(bill.getBillNo()));
        billInfoPanel.add(new JLabel("业主姓名:"));
        billInfoPanel.add(new JLabel(bill.getOwnerName()));
        billInfoPanel.add(new JLabel("费用类型:"));
        billInfoPanel.add(new JLabel(bill.getFeeTypeName()));
        billInfoPanel.add(new JLabel("账单周期:"));
        billInfoPanel.add(new JLabel(bill.getBillingPeriod()));
        billInfoPanel.add(new JLabel("应缴金额:"));
        billInfoPanel.add(new JLabel(bill.getAmount().toString()));
        billInfoPanel.add(new JLabel("已缴金额:"));
        billInfoPanel.add(new JLabel(bill.getPaidAmount().toString()));
        billInfoPanel.add(new JLabel("状态:"));
        billInfoPanel.add(new JLabel(bill.getStatus()));
        billInfoPanel.add(new JLabel("截止日期:"));
        billInfoPanel.add(new JLabel(dateFormat.format(bill.getDueDate())));
        billInfoPanel.add(new JLabel("备注:"));
        billInfoPanel.add(new JLabel(bill.getRemark() != null ? bill.getRemark() : ""));

        // 缴费记录面板
        JPanel recordPanel = new JPanel(new BorderLayout());
        recordPanel.setBorder(BorderFactory.createTitledBorder("缴费记录"));

        String[] recordColumnNames = {"记录编号", "缴费金额", "缴费方式", "缴费时间", "操作员", "收据编号"};
        DefaultTableModel recordTableModel = new DefaultTableModel(recordColumnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        JTable recordTable = new JTable(recordTableModel);
        recordTable.getTableHeader().setReorderingAllowed(false);
        recordTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        recordTable.setRowHeight(25);

        JScrollPane recordScrollPane = new JScrollPane(recordTable);
        recordPanel.add(recordScrollPane, BorderLayout.CENTER);

        // 加载缴费记录
        List<com.property.model.PaymentRecord> records = paymentService.getRecordsByBillId(id);
        SimpleDateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (com.property.model.PaymentRecord record : records) {
            Object[] rowData = {
                record.getRecordNo(),
                record.getPaymentAmount(),
                record.getPaymentMethod(),
                datetimeFormat.format(record.getPaymentTime()),
                record.getOperator(),
                record.getReceiptNo()
            };
            recordTableModel.addRow(rowData);
        }

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton closeButton = new JButton("关闭");
        buttonPanel.add(closeButton);

        // 设置布局
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.add(billInfoPanel, BorderLayout.NORTH);
        mainPanel.add(recordPanel, BorderLayout.CENTER);

        dialog.add(mainPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        closeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 显示缴费对话框
     * @param id 账单ID
     */
    private void showPaymentDialog(int id) {
        PaymentBill bill = paymentService.getBillById(id);
        if (bill == null) {
            DialogUtil.showErrorDialog(this, "获取账单信息失败");
            return;
        }

        JDialog dialog = new JDialog(this, "缴费处理", true);
        dialog.setSize(500, 400);
        dialog.setLocationRelativeTo(this);
        dialog.setLayout(new BorderLayout());

        // 账单信息面板
        JPanel billInfoPanel = new JPanel(new GridLayout(5, 2, 10, 10));
        billInfoPanel.setBorder(BorderFactory.createTitledBorder("账单信息"));

        billInfoPanel.add(new JLabel("账单编号:"));
        billInfoPanel.add(new JLabel(bill.getBillNo()));
        billInfoPanel.add(new JLabel("业主姓名:"));
        billInfoPanel.add(new JLabel(bill.getOwnerName()));
        billInfoPanel.add(new JLabel("费用类型:"));
        billInfoPanel.add(new JLabel(bill.getFeeTypeName()));
        billInfoPanel.add(new JLabel("应缴金额:"));
        billInfoPanel.add(new JLabel(bill.getAmount().toString()));
        billInfoPanel.add(new JLabel("已缴金额:"));
        billInfoPanel.add(new JLabel(bill.getPaidAmount().toString()));

        // 缴费表单面板
        JPanel formPanel = new JPanel(new GridLayout(5, 2, 10, 10));
        formPanel.setBorder(BorderFactory.createTitledBorder("缴费信息"));

        JTextField paymentAmountField = new JTextField();
        BigDecimal remainingAmount = bill.getAmount().subtract(bill.getPaidAmount());
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            paymentAmountField.setText(remainingAmount.toString());
        }

        String[] paymentMethods = {"现金", "银行转账", "在线支付", "支付宝", "微信支付"};
        JComboBox<String> paymentMethodComboBox = new JComboBox<>(paymentMethods);

        JTextField operatorField = new JTextField("管理员");
        JTextField remarkField = new JTextField();

        formPanel.add(new JLabel("缴费金额:"));
        formPanel.add(paymentAmountField);
        formPanel.add(new JLabel("缴费方式:"));
        formPanel.add(paymentMethodComboBox);
        formPanel.add(new JLabel("操作员:"));
        formPanel.add(operatorField);
        formPanel.add(new JLabel("备注:"));
        formPanel.add(remarkField);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton confirmButton = new JButton("确认缴费");
        JButton cancelButton = new JButton("取消");
        buttonPanel.add(confirmButton);
        buttonPanel.add(cancelButton);

        // 设置布局
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.add(billInfoPanel, BorderLayout.NORTH);
        mainPanel.add(formPanel, BorderLayout.CENTER);

        dialog.add(mainPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        confirmButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 验证输入
                String paymentAmountText = paymentAmountField.getText().trim();
                String operator = operatorField.getText().trim();
                String remark = remarkField.getText().trim();

                if (paymentAmountText.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入缴费金额");
                    return;
                }

                if (operator.isEmpty()) {
                    DialogUtil.showWarningDialog(dialog, "请输入操作员");
                    return;
                }

                BigDecimal paymentAmount;
                try {
                    paymentAmount = new BigDecimal(paymentAmountText);
                    if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        DialogUtil.showWarningDialog(dialog, "缴费金额必须大于0");
                        return;
                    }
                } catch (NumberFormatException ex) {
                    DialogUtil.showWarningDialog(dialog, "缴费金额必须是有效的数字");
                    return;
                }

                // 处理缴费
                String paymentMethod = (String) paymentMethodComboBox.getSelectedItem();
                boolean success = paymentService.processPayment(id, paymentAmount, paymentMethod, operator, remark);

                if (success) {
                    DialogUtil.showInfoDialog(dialog, "缴费处理成功");
                    dialog.dispose();
                    loadBills();
                } else {
                    DialogUtil.showErrorDialog(dialog, "缴费处理失败");
                }
            }
        });

        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dialog.dispose();
            }
        });

        dialog.setVisible(true);
    }

    /**
     * 删除账单
     * @param id 账单ID
     */
    private void deleteBill(int id) {
        int option = JOptionPane.showConfirmDialog(this,
                "确定要删除该账单吗？删除后无法恢复，相关的缴费记录也会被删除。",
                "确认删除",
                JOptionPane.YES_NO_OPTION);

        if (option == JOptionPane.YES_OPTION) {
            boolean success = paymentService.deleteBill(id);
            if (success) {
                DialogUtil.showInfoDialog(this, "账单删除成功");
                loadBills();
            } else {
                DialogUtil.showErrorDialog(this, "账单删除失败");
            }
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                new PaymentBillManagementView().setVisible(true);
            }
        });
    }
}
