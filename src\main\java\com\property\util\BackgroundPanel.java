package com.property.util;

import java.awt.Graphics;
import java.awt.Image;
import javax.swing.ImageIcon;
import javax.swing.JPanel;

/**
 * 背景图片面板
 * 用于在JPanel上绘制背景图片
 */
public class BackgroundPanel extends JPanel {
    private static final long serialVersionUID = 1L;
    private Image backgroundImage;
    
    /**
     * 构造函数
     * @param imagePath 图片路径
     */
    public BackgroundPanel(String imagePath) {
        try {
            backgroundImage = new ImageIcon(getClass().getResource(imagePath)).getImage();
        } catch (Exception e) {
            System.err.println("无法加载背景图片: " + e.getMessage());
        }
        setOpaque(false); // 设置面板为透明
    }
    
    /**
     * 重写绘制方法，绘制背景图片
     */
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (backgroundImage != null) {
            // 绘制图片，缩放以适应面板大小
            g.drawImage(backgroundImage, 0, 0, getWidth(), getHeight(), this);
        }
    }
}
